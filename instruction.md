# MCP OAuth 服务器开发提示词

## 项目概述

请帮我开发一个符合 MCP (Model Context Protocol) 授权规范的 OAuth 2.1 服务器系统，包含授权服务器 (Auth Server) 和 MCP 服务器 (Resource Server)。

## 核心设计原则

### 访问控制策略

- **开放客户端注册**：任何应用程序都可以动态注册获得 client_id
- **严格用户控制**：只有预定义白名单用户可以登录和授权
- **无状态设计**：使用 JWT 令牌，无需数据库存储

### 传输方式

- **优先实现**：streamable-http 传输方式
- **可选实现**：SSE（如需兼容现有客户端）
- **禁止使用**：stdio（不支持 OAuth）

## 技术规范要求

### 必须遵循的 RFC 标准

1. **OAuth 2.1** - 核心授权流程
2. **RFC7591** - 动态客户端注册协议
3. **RFC8414** - 授权服务器元数据
4. **RFC9728** - 保护资源元数据
5. **PKCE (RFC7636)** - 授权码保护（必须实现 S256 方法）

### 必须实现的端点

#### Auth Server 端点

```
/.well-known/oauth-authorization-server  # 元数据端点（必须）
/oauth/register                         # 动态客户端注册（应该支持）
/oauth/authorize                        # 授权端点（必须）
/oauth/token                           # 令牌端点（必须）
/login                                 # 用户登录页面
```

#### MCP Server 端点

```
/.well-known/oauth-protected-resource   # 资源元数据端点（必须）
/mcp                                   # Streamable HTTP 传输端点（推荐）
/sse                                   # SSE 传输端点（兼容性）
/mcp/v1/tools                          # MCP 协议端点示例
```

## 安全要求

### 必须实现的安全措施

- **HTTPS 强制**：所有端点必须使用 HTTPS
- **PKCE 验证**：客户端必须使用 S256 方法
- **重定向 URI 验证**：只允许 localhost 或 HTTPS URI
- **状态参数验证**：防止 CSRF 攻击
- **短期令牌**：访问令牌 1 小时过期
- **正确的 401 响应格式**：包含标准 WWW-Authenticate 头

### 401 响应格式要求

```
WWW-Authenticate: Bearer realm="mcp", as_uri="https://auth.example.com/.well-known/oauth-protected-resource"
```

## 配置方案

### 用户白名单配置（环境变量）

```bash
# 授权用户列表
AUTHORIZED_USERS=zhang_san,li_si,admin

# 用户认证信息
ZHANG_SAN_PASSWORD=dev123
ZHANG_SAN_SCOPES=read,write,execute

LI_SI_PASSWORD=pm456
LI_SI_SCOPES=read

ADMIN_PASSWORD=admin789
ADMIN_SCOPES=read,write,execute,admin

# 服务器配置
AUTH_SERVER_URL=https://auth.example.com
MCP_SERVER_URL=https://mcp.example.com

# 密钥配置
JWT_SECRET=your-jwt-secret-here
AUTH_CODE_SECRET=your-auth-code-secret
CLIENT_SECRET=your-client-secret
```

## 实现要点

### 1. 动态客户端注册（无状态）

- 使用自签名 JWT 作为 client_id
- 验证重定向 URI 安全性
- 无需存储客户端信息

### 2. 用户认证与授权

- 检查用户是否在白名单中
- 支持用户名密码认证
- 显示授权确认页面
- 记录用户同意的权限范围

### 3. PKCE 流程

- 客户端生成 code_verifier 和 code_challenge
- 授权码中包含 code_challenge
- 令牌交换时验证 code_verifier

### 4. JWT 令牌设计

```javascript
// 授权码 JWT 结构
{
  "client_id": "xxx",
  "code_challenge": "xxx",
  "redirect_uri": "xxx",
  "user": "zhang_san",
  "scopes": ["read", "write"],
  "exp": timestamp + 600  // 10分钟过期
}

// 访问令牌 JWT 结构
{
  "sub": "zhang_san",           // 用户标识
  "aud": "client_id",           // 客户端标识
  "scope": "read write execute", // 权限范围
  "exp": timestamp + 3600       // 1小时过期
}
```

### 5. MCP 传输支持

- 实现 streamable-http 单端点双向通信
- 实现 SSE 双端点通信（兼容性）
- 支持 MCP-Protocol-Version 头部
- 每个请求验证 Bearer Token

## 技术栈要求

### 开发语言

- **必须使用 TypeScript** - 提供类型安全和更好的开发体验

### 后端框架

- **Node.js + Express.js** 或 **Fastify**
- **Cloudflare Workers**（推荐，自带全球 CDN）

### 核心依赖

```json
{
  "@types/node": "^20.0.0", // Node.js 类型定义
  "@types/express": "^4.17.0", // Express 类型定义
  "@types/jsonwebtoken": "^9.0.0", // JWT 类型定义
  "typescript": "^5.0.0", // TypeScript 编译器
  "jsonwebtoken": "^9.0.0", // JWT 处理
  "express": "^4.18.0", // HTTP 服务器
  "cors": "^2.8.5", // CORS 支持
  "helmet": "^7.0.0" // 安全头部
}
```

## 测试要求

### 测试工具

- **MCP Inspector**：使用 streamable-http 传输测试
- **Postman/curl**：测试 OAuth 端点
- **浏览器**：测试授权流程 UI

### 测试场景

1. 客户端动态注册
2. 白名单用户登录
3. 非白名单用户被拒绝
4. PKCE 验证正确性
5. 访问令牌权限控制
6. 令牌过期处理
7. 错误响应格式

## 部署要求

### 开发环境

- 使用 `localhost` 和 HTTP（仅开发）
- 环境变量配置用户白名单
- 控制台日志输出便于调试

### 生产环境

- 必须使用 HTTPS
- 安全的密钥管理
- 限制 CORS 来源
- 生产级日志记录

## 项目结构要求

```
mcp-oauth-server/
├── src/
│   ├── auth-server.ts          # 授权服务器实现
│   ├── mcp-server.ts           # MCP 资源服务器实现
│   ├── oauth-handler.ts        # OAuth 流程处理
│   ├── user-manager.ts         # 用户管理
│   ├── jwt-utils.ts            # JWT 工具函数
│   ├── types.ts                # TypeScript 类型定义
│   └── config.ts               # 配置管理
├── public/
│   ├── login.html              # 登录页面
│   ├── authorize.html          # 授权确认页面
│   └── error.html              # 错误页面
├── tests/
│   ├── oauth-flow.test.ts      # OAuth 流程测试
│   └── mcp-endpoints.test.ts   # MCP 端点测试
├── .env                        # 环境变量
├── package.json
├── tsconfig.json               # TypeScript 配置
└── README.md
```

## 开发优先级

### Phase 1: 核心 OAuth 功能

1. 实现授权服务器元数据端点
2. 实现动态客户端注册
3. 实现用户登录和白名单验证
4. 实现授权码流程和 PKCE

### Phase 2: MCP 集成

1. 实现 MCP 服务器元数据端点
2. 实现 streamable-http 传输
3. 实现访问令牌验证
4. 添加基本的 MCP 工具端点

### Phase 3: 完善和测试

1. 完善错误处理
2. 编写完整测试套件
3. 添加生产环境配置

## 验收标准

系统完成后应该能够：

1. 任何 MCP 客户端都能通过动态注册获得 client_id
2. 只有白名单用户能成功完成 OAuth 授权流程
3. 访问令牌能正确控制 MCP 资源的访问权限
4. 完全符合 MCP 授权规范的所有要求
5. 支持主流 MCP 客户端（Claude Desktop、VS Code 插件等）

请基于以上要求开始开发，优先实现 Phase 1 的核心功能。
