const https = require('https');
const http = require('http');
const crypto = require('crypto');

// 从命令行参数获取授权码
const authCode = process.argv[2];
const codeVerifier = process.argv[3] || '8qJrvpWXDtMcBlWWgjTZ5do9h6CwNDVEI23kyc6Ka9U'; // 从之前的输出

if (!authCode) {
  console.error('Usage: node exchange-token.js <authorization_code> [code_verifier]');
  process.exit(1);
}

// 客户端 ID (与授权时使用的相同)
const jwt = require('jsonwebtoken');
const clientId = jwt.sign({
  redirect_uris: ['http://localhost:8080/callback'],
  client_name: 'MCP Inspector',
  issued_at: Math.floor(Date.now() / 1000),
  iat: Math.floor(Date.now() / 1000)
}, 'your-secret-key');

const tokenData = new URLSearchParams({
  grant_type: 'authorization_code',
  code: authCode,
  redirect_uri: 'http://localhost:8080/callback',
  client_id: clientId,
  code_verifier: codeVerifier
}).toString();

const options = {
  hostname: 'localhost',
  port: 3001,
  path: '/oauth/token',
  method: 'POST',
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded',
    'Content-Length': Buffer.byteLength(tokenData)
  }
};

const req = http.request(options, (res) => {
  let data = '';
  
  res.on('data', (chunk) => {
    data += chunk;
  });
  
  res.on('end', () => {
    try {
      const response = JSON.parse(data);
      if (response.access_token) {
        console.log('✅ Access token obtained successfully!');
        console.log('Access Token:', response.access_token);
        console.log('');
        console.log('Save this to .env.test:');
        console.log(`ACCESS_TOKEN=${response.access_token}`);
        
        // 写入文件
        const fs = require('fs');
        fs.writeFileSync('.env.test', `ACCESS_TOKEN=${response.access_token}\n`);
        console.log('✅ Token saved to .env.test');
      } else {
        console.error('❌ Error:', response);
      }
    } catch (error) {
      console.error('❌ Failed to parse response:', data);
    }
  });
});

req.on('error', (error) => {
  console.error('❌ Request failed:', error);
});

req.write(tokenData);
req.end();
