#!/bin/bash

# 令牌交换和 MCP 测试脚本

if [ $# -eq 0 ]; then
    echo "用法: $0 <授权码>"
    echo "请先运行 ./simple-test.sh 获取授权码"
    exit 1
fi

AUTH_CODE="$1"

# 从 test-info.txt 读取测试信息
if [ ! -f "test-info.txt" ]; then
    echo "错误: 找不到 test-info.txt 文件"
    echo "请先运行 ./simple-test.sh"
    exit 1
fi

CLIENT_ID=$(grep "客户端 ID:" test-info.txt | cut -d' ' -f3-)
CODE_VERIFIER=$(grep "Code Verifier:" test-info.txt | cut -d' ' -f3)

echo "🔄 交换访问令牌并测试 MCP 功能"
echo "================================"

AUTH_SERVER="http://localhost:3001"
MCP_SERVER="http://localhost:3002"
REDIRECT_URI="http://localhost:8080/callback"

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}1. 交换访问令牌${NC}"
TOKEN_RESPONSE=$(curl -s -X POST "$AUTH_SERVER/oauth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&code=$AUTH_CODE&redirect_uri=$REDIRECT_URI&client_id=$CLIENT_ID&code_verifier=$CODE_VERIFIER")

echo "令牌响应:"
echo "$TOKEN_RESPONSE" | jq .

ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r .access_token)

if [ "$ACCESS_TOKEN" = "null" ] || [ -z "$ACCESS_TOKEN" ]; then
    echo -e "${RED}❌ 令牌交换失败${NC}"
    exit 1
fi

echo -e "\n${GREEN}✅ 访问令牌获取成功!${NC}"

echo -e "\n${BLUE}2. 测试 MCP 工具列表${NC}"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/list"
  }' | jq .

echo -e "\n${BLUE}3. 测试 echo 工具${NC}"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "echo",
      "arguments": {
        "message": "Hello from MCP OAuth!"
      }
    }
  }' | jq .

echo -e "\n${BLUE}4. 测试用户信息工具${NC}"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "get_user_info",
      "arguments": {}
    }
  }' | jq .

echo -e "\n${BLUE}5. 测试计算器工具${NC}"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
      "name": "calculator",
      "arguments": {
        "operation": "add",
        "a": 15,
        "b": 27
      }
    }
  }' | jq .

echo -e "\n${BLUE}6. 测试资源读取${NC}"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 5,
    "method": "resources/read",
    "params": {
      "uri": "greeting://World"
    }
  }' | jq .

echo -e "\n${BLUE}7. 测试提示列表${NC}"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 6,
    "method": "prompts/list"
  }' | jq .

echo -e "\n${GREEN}🎉 MCP 功能测试完成!${NC}"

# 保存访问令牌供后续使用
echo "ACCESS_TOKEN=$ACCESS_TOKEN" > .env.test
echo "访问令牌已保存到 .env.test 文件"
