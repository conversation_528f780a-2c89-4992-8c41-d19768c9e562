#!/usr/bin/env node

/**
 * JWT 解码工具
 * 用于查看 client_id、authorization_code 和 access_token 的内容差异
 */

function decodeJWT(token) {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      throw new Error('Invalid JWT format');
    }

    const header = JSON.parse(Buffer.from(parts[0], 'base64url').toString());
    const payload = JSON.parse(Buffer.from(parts[1], 'base64url').toString());
    
    return {
      header,
      payload,
      signature: parts[2]
    };
  } catch (error) {
    return { error: error.message };
  }
}

function analyzeToken(token, type) {
  console.log(`\n🔍 ${type} 分析:`);
  console.log('=' .repeat(50));
  
  const decoded = decodeJWT(token);
  
  if (decoded.error) {
    console.log(`❌ 解码失败: ${decoded.error}`);
    return;
  }

  console.log('📋 Header:');
  console.log(JSON.stringify(decoded.header, null, 2));
  
  console.log('\n📦 Payload:');
  console.log(JSON.stringify(decoded.payload, null, 2));
  
  console.log(`\n🔐 Signature: ${decoded.signature.substring(0, 20)}...`);
  
  // 分析令牌类型
  const payload = decoded.payload;
  
  if (payload.redirect_uris) {
    console.log('🏷️  类型: Client ID (客户端标识)');
    console.log(`📅 签发时间: ${new Date(payload.issued_at * 1000).toLocaleString()}`);
    console.log(`🔗 重定向 URI: ${payload.redirect_uris.join(', ')}`);
    console.log(`📛 客户端名称: ${payload.client_name || 'N/A'}`);
  } else if (payload.code_challenge) {
    console.log('🏷️  类型: Authorization Code (授权码)');
    console.log(`👤 用户: ${payload.user}`);
    console.log(`🔑 权限范围: ${payload.scopes ? payload.scopes.join(', ') : 'N/A'}`);
    console.log(`🔗 重定向 URI: ${payload.redirect_uri}`);
    console.log(`⏰ 过期时间: ${new Date(payload.exp * 1000).toLocaleString()}`);
    console.log(`🔐 PKCE Challenge: ${payload.code_challenge.substring(0, 20)}...`);
  } else if (payload.sub && payload.aud) {
    console.log('🏷️  类型: Access Token (访问令牌)');
    console.log(`👤 用户 (sub): ${payload.sub}`);
    console.log(`🎯 客户端 (aud): ${payload.aud.substring(0, 30)}...`);
    console.log(`🔑 权限范围: ${payload.scope}`);
    console.log(`⏰ 过期时间: ${new Date(payload.exp * 1000).toLocaleString()}`);
    console.log(`📅 签发时间: ${new Date(payload.iat * 1000).toLocaleString()}`);
  } else {
    console.log('🏷️  类型: 未知令牌类型');
  }
}

function main() {
  console.log('🔐 JWT 令牌分析工具');
  console.log('====================');
  
  // 从 .env.test 文件读取访问令牌
  let accessToken;
  try {
    const fs = require('fs');
    const envTest = fs.readFileSync('.env.test', 'utf8');
    const match = envTest.match(/ACCESS_TOKEN=(.+)/);
    if (match) {
      accessToken = match[1].trim();
    }
  } catch (error) {
    console.log('⚠️  无法读取 .env.test 文件');
  }

  // 从 test-info.txt 读取客户端 ID
  let clientId;
  try {
    const fs = require('fs');
    const testInfo = fs.readFileSync('test-info.txt', 'utf8');
    const match = testInfo.match(/客户端 ID: (.+)/);
    if (match) {
      clientId = match[1].trim();
    }
  } catch (error) {
    console.log('⚠️  无法读取 test-info.txt 文件');
  }

  // 分析令牌
  if (clientId) {
    analyzeToken(clientId, 'Client ID');
  }

  if (accessToken) {
    analyzeToken(accessToken, 'Access Token');
  }

  // 如果提供了命令行参数
  if (process.argv.length > 2) {
    const token = process.argv[2];
    const type = process.argv[3] || 'Unknown Token';
    analyzeToken(token, type);
  }

  if (!clientId && !accessToken && process.argv.length <= 2) {
    console.log('\n📖 使用方法:');
    console.log('1. 自动分析: node jwt-decoder.js');
    console.log('2. 手动分析: node jwt-decoder.js <JWT_TOKEN> [TOKEN_TYPE]');
    console.log('\n例如:');
    console.log('node jwt-decoder.js eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9... "Authorization Code"');
  }

  console.log('\n💡 关键差异:');
  console.log('• Client ID: 包含 redirect_uris, client_name, issued_at');
  console.log('• Authorization Code: 包含 user, scopes, code_challenge, exp');
  console.log('• Access Token: 包含 sub, aud, scope, exp, iat');
  console.log('\n虽然格式相似（都是 JWT），但内容完全不同！');
}

if (require.main === module) {
  main();
}
