{"version": 3, "file": "schema.js", "sourceRoot": "", "sources": ["../../../src/rules/naming-convention-utils/schema.ts"], "names": [], "mappings": ";;;AAEA,qCAA0C;AAK1C,mCAOiB;AAEjB,MAAM,KAAK,GAA2C;IACpD,QAAQ;IACR,iBAAiB,EAAE;QACjB,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,IAAA,mBAAY,EAAC,yBAAiB,CAAC;KACtC;IACD,iBAAiB,EAAE;QACjB,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,IAAA,mBAAY,EAAC,yBAAiB,CAAC;KACtC;IACD,aAAa,EAAE;QACb,IAAI,EAAE,QAAQ;QACd,IAAI,EAAE,IAAA,mBAAY,EAAC,qBAAa,CAAC;KAClC;IAED,iBAAiB;IACjB,kBAAkB,EAAE;QAClB,IAAI,EAAE,OAAO;QACb,KAAK,EAAE;YACL,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,CAAC;SACb;QACD,eAAe,EAAE,KAAK;KACvB;IACD,gBAAgB,EAAE;QAChB,IAAI,EAAE,QAAQ;QACd,oBAAoB,EAAE,KAAK;QAC3B,UAAU,EAAE;YACV,KAAK,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE;YAC1B,KAAK,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE;SAC1B;QACD,QAAQ,EAAE,CAAC,OAAO,EAAE,OAAO,CAAC;KAC7B;IACD,mBAAmB,EAAE;QACnB,KAAK,EAAE;YACL;gBACE,IAAI,EAAE,OAAO;gBACb,KAAK,EAAE;oBACL,IAAI,EAAE,2BAA2B;iBAClC;gBACD,eAAe,EAAE,KAAK;aACvB;YACD;gBACE,IAAI,EAAE,MAAM;aACb;SACF;KACF;CACF,CAAC;AAEF,MAAM,iBAAiB,GAA2B;IAChD,IAAI,EAAE,2BAA2B;CAClC,CAAC;AACF,MAAM,oBAAoB,GAA2B;IACnD,IAAI,EAAE,4BAA4B;CACnC,CAAC;AACF,MAAM,kBAAkB,GAA2B;IACjD,IAAI,EAAE,0BAA0B;CACjC,CAAC;AAEF,MAAM,yBAAyB,GAAyB;IACtD,MAAM,EAAE;QACN,IAAI,EAAE,6BAA6B;KACpC;IACD,MAAM,EAAE,kBAAkB;IAC1B,iBAAiB,EAAE,iBAAiB;IACpC,kBAAkB,EAAE,iBAAiB;IACrC,MAAM,EAAE,oBAAoB;IAC5B,MAAM,EAAE,oBAAoB;IAC5B,cAAc,EAAE;QACd,IAAI,EAAE,QAAQ;KACf;CACF,CAAC;AACF,SAAS,cAAc,CACrB,cAAgD,EAChD,SAAkB,EAClB,SAA6B;IAE7B,MAAM,QAAQ,GAAyB;QACrC,MAAM,EAAE;YACN,KAAK,EAAE;gBACL;oBACE,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,CAAC;iBACb;gBACD,kBAAkB;aACnB;SACF;QACD,QAAQ,EAAE;YACR,IAAI,EAAE,QAAQ;YACd,IAAI,EAAE,CAAC,cAAc,CAAC;SACvB;KACF,CAAC;IACF,IAAI,SAAS,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACtC,QAAQ,CAAC,SAAS,GAAG;YACnB,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE,SAAS;aAChB;YACD,eAAe,EAAE,KAAK;SACvB,CAAC;IACJ,CAAC;IACD,IAAI,SAAS,EAAE,CAAC;QACd,QAAQ,CAAC,KAAK,GAAG;YACf,IAAI,EAAE,OAAO;YACb,KAAK,EAAE;gBACL,IAAI,EAAE,uBAAuB;aAC9B;YACD,eAAe,EAAE,KAAK;SACvB,CAAC;IACJ,CAAC;IAED,OAAO;QACL;YACE,IAAI,EAAE,QAAQ;YACd,WAAW,EAAE,aAAa,cAAc,GAAG;YAC3C,UAAU,EAAE;gBACV,GAAG,yBAAyB;gBAC5B,GAAG,QAAQ;aACZ;YACD,QAAQ,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;YAChC,oBAAoB,EAAE,KAAK;SAC5B;KACF,CAAC;AACJ,CAAC;AAED,SAAS,eAAe;IACtB,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,WAAW,EAAE,kCAAkC;QAC/C,UAAU,EAAE;YACV,GAAG,yBAAyB;YAC5B,GAAG;gBACD,MAAM,EAAE;oBACN,KAAK,EAAE;wBACL;4BACE,IAAI,EAAE,QAAQ;4BACd,SAAS,EAAE,CAAC;yBACb;wBACD,kBAAkB;qBACnB;iBACF;gBACD,QAAQ,EAAE;oBACR,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,CAAC,GAAG,IAAA,mBAAY,EAAC,qBAAa,CAAC,EAAE,GAAG,IAAA,mBAAY,EAAC,iBAAS,CAAC,CAAC;qBACnE;oBACD,eAAe,EAAE,KAAK;iBACvB;gBACD,SAAS,EAAE;oBACT,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,IAAA,mBAAY,EAAC,iBAAS,CAAC;qBAC9B;oBACD,eAAe,EAAE,KAAK;iBACvB;gBACD,KAAK,EAAE;oBACL,IAAI,EAAE,OAAO;oBACb,KAAK,EAAE;wBACL,IAAI,EAAE,uBAAuB;qBAC9B;oBACD,eAAe,EAAE,KAAK;iBACvB;aACF;SACF;QACD,QAAQ,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC;QAChC,oBAAoB,EAAE,KAAK;KAC5B,CAAC;AACJ,CAAC;AAED,MAAM,MAAM,GAA2B;IACrC,KAAK,EAAE,KAAK;IACZ,IAAI,EAAE,OAAO;IACb,KAAK,EAAE;QACL,KAAK,EAAE;YACL,eAAe,EAAE;YACjB,GAAG,cAAc,CAAC,SAAS,EAAE,KAAK,EAAE,IAAA,mBAAY,EAAC,iBAAS,CAAC,CAAC;YAE5D,GAAG,cAAc,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;YAC7D,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE;gBAClC,OAAO;gBACP,cAAc;gBACd,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,UAAU,EAAE,KAAK,EAAE;gBACnC,UAAU;gBACV,QAAQ;gBACR,QAAQ;gBACR,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,WAAW,EAAE,IAAI,EAAE,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC;YAEhE,GAAG,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE;gBACrC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;gBACV,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,eAAe,EAAE,IAAI,EAAE;gBACvC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;aACX,CAAC;YACF,GAAG,cAAc,CAAC,uBAAuB,EAAE,IAAI,EAAE;gBAC/C,QAAQ;gBACR,gBAAgB;aACjB,CAAC;YACF,GAAG,cAAc,CAAC,cAAc,EAAE,IAAI,EAAE;gBACtC,QAAQ;gBACR,UAAU;gBACV,gBAAgB;aACjB,CAAC;YACF,GAAG,cAAc,CAAC,mBAAmB,EAAE,IAAI,EAAE;gBAC3C,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,UAAU;aACX,CAAC;YACF,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE;gBAClC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;gBACV,OAAO;aACR,CAAC;YAEF,GAAG,cAAc,CAAC,aAAa,EAAE,KAAK,EAAE;gBACtC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;gBACV,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,qBAAqB,EAAE,KAAK,EAAE;gBAC9C,QAAQ;gBACR,gBAAgB;gBAChB,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAC;YACpE,GAAG,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE;gBACjC,UAAU;gBACV,SAAS;gBACT,UAAU;gBACV,WAAW;gBACX,QAAQ;gBACR,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;gBACV,OAAO;aACR,CAAC;YACF,GAAG,cAAc,CAAC,UAAU,EAAE,IAAI,EAAE;gBAClC,UAAU;gBACV,SAAS;gBACT,WAAW;gBACX,QAAQ;gBACR,gBAAgB;gBAChB,QAAQ;gBACR,UAAU;aACX,CAAC;YACF,GAAG,cAAc,CAAC,YAAY,EAAE,KAAK,EAAE,CAAC,gBAAgB,CAAC,CAAC;YAE1D,GAAG,cAAc,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YACxE,GAAG,cAAc,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;YACrE,GAAG,cAAc,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC7D,GAAG,cAAc,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YAC7D,GAAG,cAAc,CAAC,MAAM,EAAE,KAAK,EAAE,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;YACxD,GAAG,cAAc,CAAC,eAAe,EAAE,KAAK,EAAE,CAAC,QAAQ,CAAC,CAAC;YACrD,GAAG,cAAc,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;SAC7D;KACF;IACD,eAAe,EAAE,KAAK;CACvB,CAAC;AAEO,wBAAM"}