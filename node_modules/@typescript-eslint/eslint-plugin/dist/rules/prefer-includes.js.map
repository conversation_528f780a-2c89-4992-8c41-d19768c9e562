{"version": 3, "file": "prefer-includes.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-includes.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,uDAA+D;AAE/D,oDAA0D;AAC1D,wEAAiE;AACjE,+CAAiC;AAEjC,kCAKiB;AAEjB,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,iBAAiB;IACvB,cAAc,EAAE,EAAE;IAElB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,iDAAiD;YAC9D,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,cAAc,EAAE,kCAAkC;YAClD,oBAAoB,EAClB,uDAAuD;SAC1D;QACD,MAAM,EAAE,EAAE;KACX;IAED,MAAM,CAAC,OAAO;QACZ,MAAM,WAAW,GAAG,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC;QACtC,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,QAAQ,CAAC,IAAmB,EAAE,KAAa;YAClD,MAAM,SAAS,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACpD,OAAO,SAAS,IAAI,IAAI,IAAI,SAAS,CAAC,KAAK,KAAK,KAAK,CAAC;QACxD,CAAC;QAED,SAAS,eAAe,CAAC,IAA+B;YACtD,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACtB,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI,CAAC;gBACV,KAAK,GAAG;oBACN,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClC,KAAK,IAAI;oBACP,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACjC;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QACD,SAAS,eAAe,CAAC,IAA+B;YACtD,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACtB,KAAK,KAAK,CAAC;gBACX,KAAK,IAAI,CAAC;gBACV,KAAK,IAAI;oBACP,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;gBAClC,KAAK,GAAG;oBACN,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;gBACjC;oBACE,OAAO,KAAK,CAAC;YACjB,CAAC;QACH,CAAC;QAED,SAAS,iBAAiB,CACxB,KAAqB,EACrB,KAAqB;YAErB,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC3D,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC;YACjC,MAAM,OAAO,GAAG,KAAK,CAAC,UAAU,CAAC;YACjC,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM,EAAE,CAAC;gBACtC,OAAO,KAAK,CAAC;YACf,CAAC;YAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC;gBACxC,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAC1B,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;gBAE1B,6CAA6C;gBAC7C,IAAI,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,CAAC,OAAO,EAAE,EAAE,CAAC;oBAC1C,OAAO,KAAK,CAAC;gBACf,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED;;;WAGG;QACH,SAAS,WAAW,CAAC,IAAmB;YACtC,MAAM,SAAS,GAAG,IAAA,qBAAc,EAAC,IAAI,EAAE,WAAW,CAAC,CAAC;YACpD,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,CAAC,SAAS,CAAC,KAAK,YAAY,MAAM,CAAC,EAAE,CAAC;gBAC9D,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,IAAA,4BAAkB,EAAC,SAAS,CAAC,KAAK,CAAC,CAAC;YAC/D,IACE,OAAO,CAAC,YAAY,CAAC,MAAM,KAAK,CAAC;gBACjC,KAAK,CAAC,UAAU;gBAChB,KAAK,CAAC,MAAM,EACZ,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,6CAA6C;YAC7C,MAAM,KAAK,GAAG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC;YAC/C,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC;YACd,CAAC;YAED,aAAa;YACb,OAAO,MAAM,CAAC,aAAa,CACzB,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAE,CAAyB,CAAC,KAAK,CAAC,CACpD,CAAC;QACJ,CAAC;QAED,SAAS,YAAY,CAAC,GAAW;YAC/B,MAAM,SAAS,GAAG;gBAChB,IAAI,EAAE,KAAK;gBACX,GAAG,EAAE,KAAK;gBACV,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;gBACX,IAAI,EAAE,KAAK;gBACX,qCAAqC;gBACrC,eAAe;aAChB,CAAC;YACF,MAAM,YAAY,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,CAAC,CAAC;YAEzE,OAAO,GAAG,CAAC,OAAO,CAChB,YAAY,EACZ,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,IAA8B,CAAC,CAClD,CAAC;QACJ,CAAC;QAED,SAAS,iBAAiB,CACxB,IAA+B,EAC/B,WAAoB;YAEpB,yDAAyD;YACzD,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAiC,CAAC;YACxD,MAAM,WAAW,GAAG,CAClB,QAAQ,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;gBACrD,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM;gBACxB,CAAC,CAAC,QAAQ,CAAC,MAAM,CACS,CAAC;YAC/B,MAAM,QAAQ,GAAG,eAAe,CAAC,WAAW,CAAC,CAAC;YAC9C,IAAI,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;gBAC/C,OAAO;YACT,CAAC;YAED,sCAAsC;YACtC,MAAM,yBAAyB,GAAG,QAAQ;iBACvC,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnC,EAAE,eAAe,EAAE,CAAC;YACtB,IACE,yBAAyB,IAAI,IAAI;gBACjC,yBAAyB,CAAC,MAAM,KAAK,CAAC,EACtC,CAAC;gBACD,OAAO;YACT,CAAC;YAED,uEAAuE;YACvE,gDAAgD;YAChD,KAAK,MAAM,oBAAoB,IAAI,yBAAyB,EAAE,CAAC;gBAC7D,MAAM,QAAQ,GAAG,oBAAoB,CAAC,MAAM,CAAC;gBAC7C,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;gBACjD,MAAM,kBAAkB,GAAG,IAAI;qBAC5B,WAAW,CAAC,UAAU,CAAC;oBACxB,EAAE,eAAe,EAAE,CAAC;gBACtB,IACE,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,EAAE,CAC7C,iBAAiB,CAAC,kBAAkB,EAAE,oBAAoB,CAAC,CAC5D,EACD,CAAC;oBACD,OAAO;gBACT,CAAC;YACH,CAAC;YAED,aAAa;YACb,OAAO,CAAC,MAAM,CAAC;gBACb,IAAI,EAAE,WAAW;gBACjB,SAAS,EAAE,gBAAgB;gBAC3B,GAAG,CAAC,WAAW,IAAI;oBACjB,CAAC,GAAG,CAAC,KAAK;wBACR,IAAI,QAAQ,EAAE,CAAC;4BACb,MAAM,KAAK,CAAC,gBAAgB,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;wBAC9C,CAAC;wBACD,MAAM,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAC;wBACnD,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBACrE,CAAC;iBACF,CAAC;aACH,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,qBAAqB;YACrB,2GAA2G,CACzG,IAA+B;gBAE/B,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAClD,CAAC;YAED,sBAAsB;YACtB,6HAA6H,CAC3H,IAA+B;gBAE/B,iBAAiB,CAAC,IAAI,EAAE,iBAAiB,CAAC,KAAK,CAAC,CAAC;YACnD,CAAC;YAED,kBAAkB;YAClB,oGAAoG,CAClG,IAAqE;gBAErE,MAAM,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC;gBAC7B,MAAM,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACtC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,OAAO;gBACT,CAAC;gBAED,yCAAyC;gBACzC,MAAM,QAAQ,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;gBACvC,MAAM,IAAI,GAAG,IAAA,mCAA4B,EAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;gBAE9D,MAAM,kBAAkB,GAAG,IAAI;qBAC5B,WAAW,CAAC,UAAU,CAAC;oBACxB,EAAE,eAAe,EAAE,CAAC;gBACtB,IAAI,kBAAkB,IAAI,IAAI,EAAE,CAAC;oBAC/B,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI,EAAE,QAAQ;oBACd,SAAS,EAAE,sBAAsB;oBACjC,CAAC,GAAG,CAAC,KAAK;wBACR,MAAM,OAAO,GAAG,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;wBACtC,MAAM,UAAU,GACd,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;4BACvC,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;4BAC/C,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;4BAC1C,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;4BAChD,OAAO,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC;wBAEjD,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,MAAM,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;wBAC/D,IAAI,UAAU,EAAE,CAAC;4BACf,MAAM,KAAK,CAAC,gBAAgB,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;4BAC3C,MAAM,KAAK,CAAC,eAAe,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;wBAC5C,CAAC;wBACD,MAAM,KAAK,CAAC,eAAe,CACzB,OAAO,EACP,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,aAAa,YAAY,CAAC,IAAI,CAAC,IAAI,CACjE,CAAC;oBACJ,CAAC;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}