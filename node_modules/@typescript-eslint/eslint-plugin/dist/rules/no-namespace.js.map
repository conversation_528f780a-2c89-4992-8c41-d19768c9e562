{"version": 3, "file": "no-namespace.js", "sourceRoot": "", "sources": ["../../src/rules/no-namespace.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAC1D,wEAAoE;AAEpE,kCAAuD;AAUvD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EAAE,gCAAgC;YAC7C,WAAW,EAAE,aAAa;SAC3B;QACD,QAAQ,EAAE;YACR,uBAAuB,EACrB,oDAAoD;SACvD;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,iBAAiB,EAAE;wBACjB,WAAW,EACT,+DAA+D;wBACjE,IAAI,EAAE,SAAS;qBAChB;oBACD,oBAAoB,EAAE;wBACpB,WAAW,EACT,uFAAuF;wBACzF,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,iBAAiB,EAAE,KAAK;YACxB,oBAAoB,EAAE,IAAI;SAC3B;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,CAAC;QAC3D,MAAM,QAAQ,GAAG,IAAA,0BAAW,EAAC,OAAO,CAAC,CAAC;QAEtC,SAAS,aAAa,CAAC,IAAmB;YACxC,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;gBACrE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,OAAO,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3D,CAAC;QAED,OAAO;YACL,uDAAuD,CACrD,IAAkC;gBAElC,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,mBAAmB;oBACvD,CAAC,oBAAoB,IAAI,IAAA,uBAAgB,EAAC,QAAQ,CAAC,CAAC;oBACpD,CAAC,iBAAiB,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC,EAC1C,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,yBAAyB;iBACrC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}