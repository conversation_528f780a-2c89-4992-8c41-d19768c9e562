{"version": 3, "file": "no-mixed-enums.js", "sourceRoot": "", "sources": ["../../src/rules/no-mixed-enums.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oEAAkE;AAElE,oDAA0D;AAC1D,wEAAiE;AACjE,sDAAwC;AACxC,+CAAiC;AAEjC,kCAAwD;AAExD,IAAK,WAIJ;AAJD,WAAK,WAAW;IACd,iDAAM,CAAA;IACN,iDAAM,CAAA;IACN,mDAAO,CAAA;AACT,CAAC,EAJI,WAAW,KAAX,WAAW,QAIf;AAED,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,2DAA2D;YACxE,WAAW,EAAE,QAAQ;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,KAAK,EAAE,kDAAkD;SAC1D;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,cAAc,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAClD,MAAM,WAAW,GAAG,cAAc,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAO5D,SAAS,sBAAsB,CAC7B,IAAgC;YAEhC,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;YACzB,MAAM,KAAK,GAAyB;gBAClC,OAAO,EAAE,EAAE;gBACX,eAAe,EAAE,SAAS;aAC3B,CAAC;YACF,IAAI,KAAK,GAAiB,IAAA,uBAAQ,EAAC,OAAO,CAAC,CAAC;YAE5C,KAAK,MAAM,UAAU,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE,EAAE,CAAC;gBAChE,IACE,UAAU,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,iBAAiB;oBACzD,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBACxC,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAClC,CAAC;oBACD,KAAK,CAAC,eAAe,GAAG,UAAU,CAAC,IAAI,CAAC;oBACxC,MAAM;gBACR,CAAC;YACH,CAAC;YAED,OAAO,KAAK,EAAE,CAAC;gBACb,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;oBAC7C,IAAI,UAAU,CAAC,IAAI,KAAK,8BAAc,CAAC,aAAa,EAAE,CAAC;wBACrD,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;oBACtC,CAAC;gBACH,CAAC,CAAC,CAAC;gBAEH,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;YACtB,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,qBAAqB,CAAC,IAAa;YAC1C,OAAO,OAAO,CAAC,aAAa,CAC1B,WAAW,CAAC,iBAAiB,CAAC,IAAI,CAAC,EACnC,EAAE,CAAC,SAAS,CAAC,UAAU,CACxB;gBACC,CAAC,CAAC,WAAW,CAAC,MAAM;gBACpB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;QACzB,CAAC;QAED,SAAS,mBAAmB,CAC1B,QAAuB;YAEvB,MAAM,IAAI,GAAG,WAAW,CAAC,iBAAiB,CACxC,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,QAAQ,CAAC,CACnD,CAAC;YAEF,MAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,gBAAgB,CAAC;YAC5D,IACE,CAAC,gBAAgB;gBACjB,CAAC,EAAE,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;gBACvC,gBAAgB,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,EACrC,CAAC;gBACD,OAAO,SAAS,CAAC;YACnB,CAAC;YAED,OAAO,qBAAqB,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC;QAED,SAAS,aAAa,CAAC,MAA6B;YAClD,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;gBACxB,OAAO,WAAW,CAAC,MAAM,CAAC;YAC5B,CAAC;YAED,QAAQ,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;gBAChC,KAAK,sBAAc,CAAC,OAAO;oBACzB,QAAQ,OAAO,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;wBACxC,KAAK,QAAQ;4BACX,OAAO,WAAW,CAAC,MAAM,CAAC;wBAC5B,KAAK,QAAQ;4BACX,OAAO,WAAW,CAAC,MAAM,CAAC;wBAC5B;4BACE,OAAO,WAAW,CAAC,OAAO,CAAC;oBAC/B,CAAC;gBAEH,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO,WAAW,CAAC,MAAM,CAAC;gBAE5B;oBACE,OAAO,qBAAqB,CAC1B,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,MAAM,CAAC,WAAW,CAAC,CAC7D,CAAC;YACN,CAAC;QACH,CAAC;QAED,SAAS,2BAA2B,CAClC,IAAgC;YAEhC,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,GAAG,sBAAsB,CAAC,IAAI,CAAC,CAAC;YAElE,iDAAiD;YACjD,yCAAyC;YACzC,kCAAkC;YAClC,sBAAsB;YACtB,IAAI;YACJ,KAAK,MAAM,QAAQ,IAAI,OAAO,EAAE,CAAC;gBAC/B,MAAM,gBAAgB,GAAG,mBAAmB,CAAC,QAAQ,CAAC,CAAC;gBACvD,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;oBACnC,OAAO,gBAAgB,CAAC;gBAC1B,CAAC;YACH,CAAC;YAED,oDAAoD;YACpD,oBAAoB;YACpB,oBAAoB;YACpB,IAAI,eAAe,EAAE,CAAC;gBACpB,OAAO,aAAa,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;YACnD,CAAC;YAED,sCAAsC;YACtC,0BAA0B;YAC1B,6BAA6B;YAC7B,IAAI;YACJ,0BAA0B;YAC1B,6BAA6B;YAC7B,IAAI;YACJ,IACE,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB;gBAC1D,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EACxD,CAAC;gBACD,oEAAoE;gBACpE,qDAAqD;gBACrD,iEAAiE;gBACjE,MAAM,MAAM,GAAG,cAAc,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;gBACjE,MAAM,YAAY,GAAG,WAAW;qBAC7B,mBAAmB,CAAC,MAAM,CAAE;qBAC5B,eAAe,EAAG,CAAC;gBAEtB,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE,CAAC;oBACvC,KAAK,MAAM,MAAM,IAAK,WAAkC,CAAC,OAAO,EAAE,CAAC;wBACjE,OAAO,MAAM,CAAC,WAAW;4BACvB,CAAC,CAAC,OAAO,CAAC,aAAa,CACnB,WAAW,CAAC,iBAAiB,CAAC,MAAM,CAAC,WAAW,CAAC,EACjD,EAAE,CAAC,SAAS,CAAC,UAAU,CACxB;gCACD,CAAC,CAAC,WAAW,CAAC,MAAM;gCACpB,CAAC,CAAC,WAAW,CAAC,MAAM;4BACtB,CAAC,CAAC,WAAW,CAAC,MAAM,CAAC;oBACzB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,2DAA2D;YAC3D,OAAO,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QACxC,CAAC;QAED,OAAO;YACL,iBAAiB,CAAC,IAAI;gBACpB,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC;oBACzB,OAAO;gBACT,CAAC;gBAED,IAAI,WAAW,GAAG,2BAA2B,CAAC,IAAI,CAAC,CAAC;gBACpD,IAAI,WAAW,KAAK,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;oBACzC,OAAO;gBACT,CAAC;gBAED,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBAClC,MAAM,WAAW,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;oBAC1C,IAAI,WAAW,KAAK,WAAW,CAAC,OAAO,EAAE,CAAC;wBACxC,OAAO;oBACT,CAAC;oBAED,IAAI,WAAW,KAAK,WAAW,CAAC,MAAM,EAAE,CAAC;wBACvC,WAAW,KAAK,WAAW,CAAC;oBAC9B,CAAC;oBAED,IAAI,WAAW,KAAK,WAAW,EAAE,CAAC;wBAChC,OAAO,CAAC,MAAM,CAAC;4BACb,SAAS,EAAE,OAAO;4BAClB,IAAI,EAAE,MAAM,CAAC,WAAW,IAAI,MAAM;yBACnC,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;gBACH,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}