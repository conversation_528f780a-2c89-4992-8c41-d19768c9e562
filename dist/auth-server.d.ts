declare module 'express-session' {
    interface SessionData {
        user?: string;
        authParams?: {
            response_type: string;
            client_id: string;
            redirect_uri: string;
            scope: string;
            state: string;
            code_challenge: string;
            code_challenge_method: string;
        };
    }
}
export declare class AuthServer {
    private app;
    private server;
    constructor();
    private setupMiddleware;
    private setupRoutes;
    private handleLogin;
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=auth-server.d.ts.map