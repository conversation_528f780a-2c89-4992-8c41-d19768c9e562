"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Config = void 0;
const dotenv_1 = __importDefault(require("dotenv"));
// 加载环境变量
dotenv_1.default.config();
class Config {
    // 获取授权用户列表
    static getAuthorizedUsers() {
        const authorizedUsers = process.env.AUTHORIZED_USERS?.split(',') || [];
        return authorizedUsers.map(username => {
            const passwordKey = `${username.toUpperCase()}_PASSWORD`;
            const scopesKey = `${username.toUpperCase()}_SCOPES`;
            const password = process.env[passwordKey];
            const scopes = process.env[scopesKey]?.split(',') || [];
            if (!password) {
                throw new Error(`Password not configured for user: ${username}`);
            }
            return {
                username: username.trim(),
                password,
                scopes
            };
        });
    }
    // 验证配置
    static validateConfig() {
        const requiredEnvVars = [
            'JWT_SECRET',
            'AUTH_CODE_SECRET',
            'CLIENT_SECRET',
            'AUTHORIZED_USERS'
        ];
        for (const envVar of requiredEnvVars) {
            if (!process.env[envVar]) {
                throw new Error(`Required environment variable ${envVar} is not set`);
            }
        }
        // 验证用户配置
        try {
            Config.getAuthorizedUsers();
        }
        catch (error) {
            throw new Error(`User configuration error: ${error}`);
        }
    }
    // 获取重定向 URI 验证规则
    static isValidRedirectUri(uri) {
        try {
            const url = new URL(uri);
            // 开发环境允许 localhost 和 HTTP
            if (Config.IS_DEVELOPMENT) {
                return url.hostname === 'localhost' || url.hostname === '127.0.0.1' || url.protocol === 'https:';
            }
            // 生产环境只允许 HTTPS
            return url.protocol === 'https:';
        }
        catch {
            return false;
        }
    }
}
exports.Config = Config;
// 服务器配置
Config.AUTH_SERVER_PORT = parseInt(process.env.AUTH_SERVER_PORT || '3001');
Config.MCP_SERVER_PORT = parseInt(process.env.MCP_SERVER_PORT || '3002');
Config.AUTH_SERVER_URL = process.env.AUTH_SERVER_URL || `http://localhost:${Config.AUTH_SERVER_PORT}`;
Config.MCP_SERVER_URL = process.env.MCP_SERVER_URL || `http://localhost:${Config.MCP_SERVER_PORT}`;
// 密钥配置
Config.JWT_SECRET = process.env.JWT_SECRET || 'your-jwt-secret-here';
Config.AUTH_CODE_SECRET = process.env.AUTH_CODE_SECRET || 'your-auth-code-secret';
Config.CLIENT_SECRET = process.env.CLIENT_SECRET || 'your-client-secret';
// 环境配置
Config.NODE_ENV = process.env.NODE_ENV || 'development';
Config.IS_DEVELOPMENT = Config.NODE_ENV === 'development';
// OAuth 配置
Config.ACCESS_TOKEN_EXPIRES_IN = 3600; // 1 小时
Config.AUTH_CODE_EXPIRES_IN = 600; // 10 分钟
// 支持的权限范围
Config.SUPPORTED_SCOPES = ['read', 'write', 'execute', 'admin'];
//# sourceMappingURL=config.js.map