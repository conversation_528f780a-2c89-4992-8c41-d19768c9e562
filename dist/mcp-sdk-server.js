"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPSDKServer = void 0;
const mcp_js_1 = require("@modelcontextprotocol/sdk/server/mcp.js");
const stdio_js_1 = require("@modelcontextprotocol/sdk/server/stdio.js");
const zod_1 = require("zod");
const config_1 = require("./config");
const user_manager_1 = require("./user-manager");
const jwt_utils_1 = require("./jwt-utils");
class MCPSDKServer {
    constructor() {
        this.currentUser = null;
        this.server = new mcp_js_1.McpServer({
            name: "mcp-oauth-server",
            version: "1.0.0"
        });
        this.setupTools();
        this.setupResources();
        this.setupPrompts();
    }
    /**
     * 设置访问令牌（用于权限验证）
     */
    setAccessToken(token) {
        try {
            this.currentUser = jwt_utils_1.JWTUtils.verifyAccessToken(token);
        }
        catch (error) {
            throw new Error(`Invalid access token: ${error}`);
        }
    }
    /**
     * 检查用户是否有指定权限
     */
    hasScope(scope) {
        if (!this.currentUser)
            return false;
        const userScopes = this.currentUser.scope.split(' ');
        return userScopes.includes(scope);
    }
    /**
     * 设置 MCP 工具
     */
    setupTools() {
        // Echo 工具 - 回显输入消息
        this.server.registerTool("echo", {
            title: "Echo Tool",
            description: "Echo back the input message",
            inputSchema: {
                message: zod_1.z.string().describe("Message to echo back")
            }
        }, async ({ message }) => {
            return {
                content: [
                    {
                        type: "text",
                        text: `Echo: ${message}`
                    }
                ]
            };
        });
        // 用户信息工具 - 需要 read 权限
        this.server.registerTool("get_user_info", {
            title: "Get User Info",
            description: "Get current authenticated user information",
            inputSchema: {}
        }, async () => {
            if (!this.hasScope('read')) {
                throw new Error('Insufficient permissions: read scope required');
            }
            if (!this.currentUser) {
                throw new Error('No authenticated user');
            }
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify({
                            user: this.currentUser.sub,
                            scopes: this.currentUser.scope.split(' '),
                            client: this.currentUser.aud,
                            expires: new Date(this.currentUser.exp * 1000).toISOString()
                        }, null, 2)
                    }
                ]
            };
        });
        // 计算器工具 - 需要 execute 权限
        this.server.registerTool("calculator", {
            title: "Calculator",
            description: "Perform basic arithmetic operations",
            inputSchema: {
                operation: zod_1.z.enum(["add", "subtract", "multiply", "divide"]).describe("The arithmetic operation to perform"),
                a: zod_1.z.number().describe("First number"),
                b: zod_1.z.number().describe("Second number")
            }
        }, async ({ operation, a, b }) => {
            if (!this.hasScope('execute')) {
                throw new Error('Insufficient permissions: execute scope required');
            }
            let result;
            switch (operation) {
                case 'add':
                    result = a + b;
                    break;
                case 'subtract':
                    result = a - b;
                    break;
                case 'multiply':
                    result = a * b;
                    break;
                case 'divide':
                    if (b === 0) {
                        throw new Error('Division by zero is not allowed');
                    }
                    result = a / b;
                    break;
                default:
                    throw new Error(`Unknown operation: ${operation}`);
            }
            return {
                content: [
                    {
                        type: "text",
                        text: `${a} ${operation} ${b} = ${result}`
                    }
                ]
            };
        });
        // 系统信息工具 - 需要 admin 权限
        this.server.registerTool("system_info", {
            title: "System Info",
            description: "Get system and server information (admin only)",
            inputSchema: {}
        }, async () => {
            if (!this.hasScope('admin')) {
                throw new Error('Insufficient permissions: admin scope required');
            }
            return {
                content: [
                    {
                        type: "text",
                        text: JSON.stringify({
                            server: {
                                name: "MCP OAuth Server",
                                version: "1.0.0",
                                auth_server_url: config_1.Config.AUTH_SERVER_URL,
                                mcp_server_url: config_1.Config.MCP_SERVER_URL
                            },
                            users: {
                                total: user_manager_1.UserManager.getAllUsers().length,
                                authorized_users: user_manager_1.UserManager.getAllUsers()
                            },
                            scopes: {
                                supported: config_1.Config.SUPPORTED_SCOPES
                            },
                            environment: {
                                node_env: config_1.Config.NODE_ENV,
                                is_development: config_1.Config.IS_DEVELOPMENT
                            }
                        }, null, 2)
                    }
                ]
            };
        });
    }
    /**
     * 设置 MCP 资源
     */
    setupResources() {
        // 用户配置文件资源
        this.server.registerResource("user_profile", new mcp_js_1.ResourceTemplate("user://{username}", { list: undefined }), {
            title: "User Profile",
            description: "Get user profile information"
        }, async (uri, { username }) => {
            if (!this.hasScope('read')) {
                throw new Error('Insufficient permissions: read scope required');
            }
            const user = user_manager_1.UserManager.getUser(username);
            if (!user) {
                throw new Error(`User not found: ${username}`);
            }
            return {
                contents: [{
                        uri: uri.href,
                        mimeType: "application/json",
                        text: JSON.stringify({
                            username: user.username,
                            scopes: user.scopes,
                            authorized: true
                        }, null, 2)
                    }]
            };
        });
        // 动态问候资源
        this.server.registerResource("greeting", new mcp_js_1.ResourceTemplate("greeting://{name}", { list: undefined }), {
            title: "Greeting Resource",
            description: "Dynamic greeting generator"
        }, async (uri, { name }) => {
            const greeting = this.currentUser
                ? `Hello, ${name}! You are authenticated as ${this.currentUser.sub}.`
                : `Hello, ${name}! You are not authenticated.`;
            return {
                contents: [{
                        uri: uri.href,
                        mimeType: "text/plain",
                        text: greeting
                    }]
            };
        });
        // 服务器状态资源 - 需要 admin 权限
        this.server.registerResource("server_status", new mcp_js_1.ResourceTemplate("status://server", { list: undefined }), {
            title: "Server Status",
            description: "Get server status information (admin only)"
        }, async (uri) => {
            if (!this.hasScope('admin')) {
                throw new Error('Insufficient permissions: admin scope required');
            }
            return {
                contents: [{
                        uri: uri.href,
                        mimeType: "application/json",
                        text: JSON.stringify({
                            status: "healthy",
                            timestamp: new Date().toISOString(),
                            uptime: process.uptime(),
                            memory: process.memoryUsage(),
                            current_user: this.currentUser?.sub || null
                        }, null, 2)
                    }]
            };
        });
    }
    /**
     * 设置 MCP 提示
     */
    setupPrompts() {
        // 用户助手提示
        this.server.registerPrompt("user_assistant", {
            title: "User Assistant",
            description: "A helpful assistant for the authenticated user"
        }, async () => {
            const userName = this.currentUser?.sub || "Guest";
            const userScopes = this.currentUser?.scope.split(' ') || [];
            return {
                messages: [
                    {
                        role: "user",
                        content: {
                            type: "text",
                            text: `You are a helpful assistant for ${userName}. The user has the following permissions: ${userScopes.join(', ')}. Please assist them with tasks within their permission scope. Hello! I need help with my tasks.`
                        }
                    }
                ]
            };
        });
        // 权限检查提示
        this.server.registerPrompt("permission_check", {
            title: "Permission Check",
            description: "Check what permissions the current user has"
        }, async () => {
            const text = this.currentUser
                ? `Current user: ${this.currentUser.sub}\nPermissions: ${this.currentUser.scope}\nClient: ${this.currentUser.aud}\nToken expires: ${new Date(this.currentUser.exp * 1000).toISOString()}`
                : "No user is currently authenticated. Please authenticate first to check permissions.";
            return {
                messages: [
                    {
                        role: "user",
                        content: {
                            type: "text",
                            text
                        }
                    }
                ]
            };
        });
    }
    /**
     * 启动 MCP 服务器（Stdio 传输）
     */
    async startStdio() {
        const transport = new stdio_js_1.StdioServerTransport();
        await this.server.connect(transport);
    }
    /**
     * 获取服务器实例（用于其他传输方式）
     */
    getServer() {
        return this.server;
    }
}
exports.MCPSDKServer = MCPSDKServer;
//# sourceMappingURL=mcp-sdk-server.js.map