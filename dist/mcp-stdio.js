#!/usr/bin/env node
"use strict";
/**
 * MCP OAuth Server - Stdio Transport
 *
 * 这个文件提供了一个独立的 MCP 服务器，使用 stdio 传输。
 * 它可以被 MCP 客户端直接调用，通过标准输入/输出进行通信。
 *
 * 使用方法：
 * node dist/mcp-stdio.js
 *
 * 或者在 MCP 客户端配置中：
 * {
 *   "command": "node",
 *   "args": ["path/to/dist/mcp-stdio.js"]
 * }
 */
Object.defineProperty(exports, "__esModule", { value: true });
const mcp_sdk_server_1 = require("./mcp-sdk-server");
const config_1 = require("./config");
const user_manager_1 = require("./user-manager");
async function main() {
    try {
        // 验证配置
        config_1.Config.validateConfig();
        // 初始化用户管理器
        user_manager_1.UserManager.initialize();
        // 创建 MCP 服务器
        const mcpServer = new mcp_sdk_server_1.MCPSDKServer();
        // 如果有环境变量中的访问令牌，设置它
        const accessToken = process.env.MCP_ACCESS_TOKEN;
        if (accessToken) {
            try {
                mcpServer.setAccessToken(accessToken);
                console.error('✅ Access token validated');
            }
            catch (error) {
                console.error('❌ Invalid access token:', error);
                process.exit(1);
            }
        }
        else {
            console.error('⚠️  No access token provided. Some features may be restricted.');
        }
        console.error('🚀 MCP OAuth Server (Stdio) starting...');
        // 启动 stdio 传输
        await mcpServer.startStdio();
    }
    catch (error) {
        console.error('❌ Failed to start MCP server:', error);
        process.exit(1);
    }
}
// 处理未捕获的异常
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    process.exit(1);
});
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    process.exit(1);
});
// 启动服务器
main().catch((error) => {
    console.error('Failed to start:', error);
    process.exit(1);
});
//# sourceMappingURL=mcp-stdio.js.map