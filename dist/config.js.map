{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../src/config.ts"], "names": [], "mappings": ";;;;;;AAAA,oDAA4B;AAG5B,SAAS;AACT,gBAAM,CAAC,MAAM,EAAE,CAAC;AAEhB,MAAa,MAAM;IAuBjB,WAAW;IACX,MAAM,CAAC,kBAAkB;QACvB,MAAM,eAAe,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QAEvE,OAAO,eAAe,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;YACpC,MAAM,WAAW,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,WAAW,CAAC;YACzD,MAAM,SAAS,GAAG,GAAG,QAAQ,CAAC,WAAW,EAAE,SAAS,CAAC;YAErD,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,WAAW,CAAC,CAAC;YAC1C,MAAM,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAExD,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,KAAK,CAAC,qCAAqC,QAAQ,EAAE,CAAC,CAAC;YACnE,CAAC;YAED,OAAO;gBACL,QAAQ,EAAE,QAAQ,CAAC,IAAI,EAAE;gBACzB,QAAQ;gBACR,MAAM;aACP,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAED,OAAO;IACP,MAAM,CAAC,cAAc;QACnB,MAAM,eAAe,GAAG;YACtB,YAAY;YACZ,kBAAkB;YAClB,eAAe;YACf,kBAAkB;SACnB,CAAC;QAEF,KAAK,MAAM,MAAM,IAAI,eAAe,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;gBACzB,MAAM,IAAI,KAAK,CAAC,iCAAiC,MAAM,aAAa,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAED,SAAS;QACT,IAAI,CAAC;YACH,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC9B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,6BAA6B,KAAK,EAAE,CAAC,CAAC;QACxD,CAAC;IACH,CAAC;IAED,iBAAiB;IACjB,MAAM,CAAC,kBAAkB,CAAC,GAAW;QACnC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;YAEzB,0BAA0B;YAC1B,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC;gBAC1B,OAAO,GAAG,CAAC,QAAQ,KAAK,WAAW,IAAI,GAAG,CAAC,QAAQ,KAAK,WAAW,IAAI,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;YACnG,CAAC;YAED,gBAAgB;YAChB,OAAO,GAAG,CAAC,QAAQ,KAAK,QAAQ,CAAC;QACnC,CAAC;QAAC,MAAM,CAAC;YACP,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;;AApFH,wBAqFC;AApFC,QAAQ;AACQ,uBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,MAAM,CAAC,CAAC;AACpE,sBAAe,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,MAAM,CAAC,CAAC;AAClE,sBAAe,GAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,oBAAoB,MAAM,CAAC,gBAAgB,EAAE,CAAC;AAC/F,qBAAc,GAAG,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,oBAAoB,MAAM,CAAC,eAAe,EAAE,CAAC;AAE5G,OAAO;AACS,iBAAU,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,sBAAsB,CAAC;AAC9D,uBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,IAAI,uBAAuB,CAAC;AAC3E,oBAAa,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI,oBAAoB,CAAC;AAElF,OAAO;AACS,eAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa,CAAC;AACjD,qBAAc,GAAG,MAAM,CAAC,QAAQ,KAAK,aAAa,CAAC;AAEnE,WAAW;AACK,8BAAuB,GAAG,IAAI,CAAC,CAAC,OAAO;AACvC,2BAAoB,GAAG,GAAG,CAAC,CAAK,QAAQ;AAExD,UAAU;AACM,uBAAgB,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC"}