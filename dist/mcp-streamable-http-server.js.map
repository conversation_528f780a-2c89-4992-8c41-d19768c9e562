{"version": 3, "file": "mcp-streamable-http-server.js", "sourceRoot": "", "sources": ["../src/mcp-streamable-http-server.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA8D;AAC9D,gDAAwB;AACxB,oDAA4B;AAC5B,oEAAsF;AACtF,0FAAmG;AACnG,6BAAwB;AACxB,qCAAkC;AAClC,2CAAuC;AACvC,mDAA+C;AAC/C,iDAA6C;AAG7C,MAAa,uBAAuB;IAKlC;QAFQ,eAAU,GAA+C,IAAI,GAAG,EAAE,CAAC;QAGzE,IAAI,CAAC,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;QACrB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,eAAe,CAAC,IAAiB;QACvC,MAAM,MAAM,GAAG,IAAI,kBAAS,CAAC;YAC3B,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QAEH,UAAU;QACV,MAAM,CAAC,YAAY,CACjB,MAAM,EACN;YACE,KAAK,EAAE,WAAW;YAClB,WAAW,EAAE,6BAA6B;YAC1C,WAAW,EAAE;gBACX,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC;aACrD;SACF,EACD,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YACpB,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,SAAS,OAAO,EAAE;qBACzB;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,SAAS;QACT,MAAM,CAAC,YAAY,CACjB,eAAe,EACf;YACE,KAAK,EAAE,eAAe;YACtB,WAAW,EAAE,4CAA4C;YACzD,WAAW,EAAE,EAAE;SAChB,EACD,KAAK,IAAI,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,EAAE,CAAC;gBACjC,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACnE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,IAAI,EAAE,IAAI,CAAC,GAAG;4BACd,MAAM,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;4BAC7B,MAAM,EAAE,IAAI,CAAC,GAAG;4BAChB,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;yBACjD,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,QAAQ;QACR,MAAM,CAAC,YAAY,CACjB,YAAY,EACZ;YACE,KAAK,EAAE,YAAY;YACnB,WAAW,EAAE,qCAAqC;YAClD,WAAW,EAAE;gBACX,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,qCAAqC,CAAC;gBAC5G,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;gBACtC,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;aACxC;SACF,EACD,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,EAAE,CAAC;gBACpC,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,MAAc,CAAC;YACnB,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,KAAK;oBACR,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR,KAAK,UAAU;oBACb,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR,KAAK,UAAU;oBACb,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;wBACZ,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;oBACrD,CAAC;oBACD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;YACvD,CAAC;YAED,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,GAAG,CAAC,IAAI,SAAS,IAAI,CAAC,MAAM,MAAM,EAAE;qBAC3C;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,SAAS;QACT,MAAM,CAAC,YAAY,CACjB,aAAa,EACb;YACE,KAAK,EAAE,aAAa;YACpB,WAAW,EAAE,gDAAgD;YAC7D,WAAW,EAAE,EAAE;SAChB,EACD,KAAK,IAAI,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,EAAE,CAAC;gBAClC,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,MAAM,EAAE;gCACN,IAAI,EAAE,kBAAkB;gCACxB,OAAO,EAAE,OAAO;gCAChB,eAAe,EAAE,eAAM,CAAC,eAAe;gCACvC,cAAc,EAAE,eAAM,CAAC,cAAc;6BACtC;4BACD,KAAK,EAAE;gCACL,KAAK,EAAE,0BAAW,CAAC,WAAW,EAAE,CAAC,MAAM;gCACvC,gBAAgB,EAAE,0BAAW,CAAC,WAAW,EAAE;6BAC5C;4BACD,MAAM,EAAE;gCACN,SAAS,EAAE,eAAM,CAAC,gBAAgB;6BACnC;4BACD,WAAW,EAAE;gCACX,QAAQ,EAAE,eAAM,CAAC,QAAQ;gCACzB,cAAc,EAAE,eAAM,CAAC,cAAc;6BACtC;yBACF,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,KAAK;QACL,MAAM,CAAC,gBAAgB,CACrB,UAAU,EACV,IAAI,yBAAgB,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC9D;YACE,KAAK,EAAE,mBAAmB;YAC1B,WAAW,EAAE,4BAA4B;SAC1C,EACD,KAAK,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;YACtB,OAAO;gBACL,QAAQ,EAAE,CAAC;wBACT,GAAG,EAAE,GAAG,CAAC,IAAI;wBACb,QAAQ,EAAE,YAAY;wBACtB,IAAI,EAAE,UAAU,IAAI,8BAA8B,IAAI,CAAC,GAAG,GAAG;qBAC9D,CAAC;aACH,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,KAAK;QACL,MAAM,CAAC,cAAc,CACnB,gBAAgB,EAChB;YACE,KAAK,EAAE,gBAAgB;YACvB,WAAW,EAAE,gDAAgD;YAC7D,UAAU,EAAE,EAAE;SACf,EACD,GAAG,EAAE;YACH,OAAO;gBACL,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE;4BACP,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,mCAAmC,IAAI,CAAC,GAAG,+BAA+B,IAAI,CAAC,KAAK,GAAG;yBAC9F;qBACF;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,QAAQ,CAAC,IAAiB,EAAE,KAAa;QAC/C,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACzC,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAIO,eAAe;QACrB,QAAQ;QACR,IAAI,eAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,EAAC;gBAClB,qBAAqB,EAAE,KAAK;aAC7B,CAAC,CAAC,CAAC;QACN,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,GAAE,CAAC,CAAC;QACzB,CAAC;QAED,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC;YAChB,MAAM,EAAE,IAAI;YACZ,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,QAAQ,CAAC;YAC7C,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,eAAe,EAAE,QAAQ,CAAC;YAC9F,cAAc,EAAE,CAAC,gBAAgB,CAAC;SACnC,CAAC,CAAC,CAAC;QAEJ,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEO,iBAAiB,CAAC,GAAY,EAAE,GAAa,EAAE,IAAS;QAC9D,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAErD,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;gBAClB,kBAAkB,EAAE,+BAA+B,eAAM,CAAC,eAAe,0CAA0C;aACpH,CAAC,CAAC,IAAI,CAAC;gBACN,KAAK,EAAE,eAAe;gBACtB,iBAAiB,EAAE,sBAAsB;aAC1C,CAAC,CAAC;YACH,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAgB,oBAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9D,GAAW,CAAC,IAAI,GAAG,OAAO,CAAC;YAC5B,IAAI,EAAE,CAAC;QACT,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;gBAClB,kBAAkB,EAAE,+BAA+B,eAAM,CAAC,eAAe,0CAA0C;aACpH,CAAC,CAAC,IAAI,CAAC;gBACN,KAAK,EAAE,eAAe;gBACtB,iBAAiB,EAAE,iCAAiC;aACrD,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,WAAW;QACjB,aAAa;QACb,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uCAAuC,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACpF,GAAG,CAAC,IAAI,CAAC,4BAAY,CAAC,yBAAyB,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YAC5F,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACtD,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,4BAA4B;aACtC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,GAAY,EAAE,GAAa,EAAE,IAAS,EAAE,EAAE;YAChE,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,GAAG,CAAC,CAAC;YACxD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,uBAAuB;aAC3C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,WAAW;gBAClB,iBAAiB,EAAE,oBAAoB;aACxC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAY,EAAE,GAAa;QACxD,IAAI,CAAC;YACH,MAAM,IAAI,GAAI,GAAW,CAAC,IAAmB,CAAC;YAC9C,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAW,CAAC;YAE1D,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,EAAE,MAAM,IAAI,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC;YAElF,IAAI,SAAwC,CAAC;YAE7C,IAAI,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE,CAAC;gBAChD,SAAS;gBACT,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,CAAE,CAAC;YAC9C,CAAC;iBAAM,CAAC;gBACN,aAAa;gBACb,SAAS,GAAG,IAAI,iDAA6B,CAAC;oBAC5C,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAI,CAAC,iBAAiB,EAAE;oBAClD,oBAAoB,EAAE,CAAC,SAAS,EAAE,EAAE;wBAClC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;oBAC5C,CAAC;iBACF,CAAC,CAAC;gBAEH,OAAO;gBACP,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;oBACvB,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;wBACxB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;oBAC9C,CAAC;gBACH,CAAC,CAAC;gBAEF,eAAe;gBACf,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE7C,QAAQ;gBACR,MAAM,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;YAED,OAAO;YACP,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QAEpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC;gBACrB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,EAAE,EAAE,IAAI;oBACR,KAAK,EAAE;wBACL,IAAI,EAAE,CAAC,KAAK;wBACZ,OAAO,EAAE,gBAAgB;wBACzB,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC;qBACpB;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAEO,iBAAiB;QACvB,OAAO,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC;IAC3E,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,eAAM,CAAC,eAAe,EAAE,GAAG,EAAE;oBACzD,OAAO,CAAC,GAAG,CAAC,mDAAmD,eAAM,CAAC,eAAe,EAAE,CAAC,CAAC;oBACzF,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE;oBACrC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;wBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,eAAM,CAAC,eAAe,oBAAoB,CAAC,CAAC,CAAC;oBACxE,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;oBACrB,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;oBACrD,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAvYD,0DAuYC"}