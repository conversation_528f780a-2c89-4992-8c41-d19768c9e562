{"version": 3, "file": "mcp-stdio.js", "sourceRoot": "", "sources": ["../src/mcp-stdio.ts"], "names": [], "mappings": ";;AAEA;;;;;;;;;;;;;;GAcG;;AAEH,qDAAgD;AAChD,qCAAkC;AAClC,iDAA6C;AAE7C,KAAK,UAAU,IAAI;IACjB,IAAI,CAAC;QACH,OAAO;QACP,eAAM,CAAC,cAAc,EAAE,CAAC;QAExB,WAAW;QACX,0BAAW,CAAC,UAAU,EAAE,CAAC;QAEzB,aAAa;QACb,MAAM,SAAS,GAAG,IAAI,6BAAY,EAAE,CAAC;QAErC,oBAAoB;QACpB,MAAM,WAAW,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC;QACjD,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,CAAC;gBACH,SAAS,CAAC,cAAc,CAAC,WAAW,CAAC,CAAC;gBACtC,OAAO,CAAC,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC5C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;gBAChD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YAClB,CAAC;QACH,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,KAAK,CAAC,gEAAgE,CAAC,CAAC;QAClF,CAAC;QAED,OAAO,CAAC,KAAK,CAAC,yCAAyC,CAAC,CAAC;QAEzD,cAAc;QACd,MAAM,SAAS,CAAC,UAAU,EAAE,CAAC;IAE/B,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;QACtD,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAClB,CAAC;AACH,CAAC;AAED,WAAW;AACX,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,CAAC,KAAK,EAAE,EAAE;IACxC,OAAO,CAAC,KAAK,CAAC,qBAAqB,EAAE,KAAK,CAAC,CAAC;IAC5C,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,EAAE;IACnD,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,CAAC,CAAC;IACrE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC;AAEH,QAAQ;AACR,IAAI,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;IACrB,OAAO,CAAC,KAAK,CAAC,kBAAkB,EAAE,KAAK,CAAC,CAAC;IACzC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAClB,CAAC,CAAC,CAAC"}