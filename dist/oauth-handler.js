"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.OAuthHandler = void 0;
const config_1 = require("./config");
const user_manager_1 = require("./user-manager");
const jwt_utils_1 = require("./jwt-utils");
class OAuthHandler {
    /**
     * 获取授权服务器元数据
     */
    static getAuthServerMetadata() {
        return {
            issuer: config_1.Config.AUTH_SERVER_URL,
            authorization_endpoint: `${config_1.Config.AUTH_SERVER_URL}/oauth/authorize`,
            token_endpoint: `${config_1.Config.AUTH_SERVER_URL}/oauth/token`,
            registration_endpoint: `${config_1.Config.AUTH_SERVER_URL}/oauth/register`,
            scopes_supported: config_1.Config.SUPPORTED_SCOPES,
            response_types_supported: ['code'],
            grant_types_supported: ['authorization_code'],
            token_endpoint_auth_methods_supported: ['none'],
            code_challenge_methods_supported: ['S256']
        };
    }
    /**
     * 获取资源服务器元数据
     */
    static getResourceServerMetadata() {
        return {
            resource: config_1.Config.MCP_SERVER_URL,
            authorization_servers: [config_1.Config.AUTH_SERVER_URL],
            scopes_supported: config_1.Config.SUPPORTED_SCOPES,
            bearer_methods_supported: ['header']
        };
    }
    /**
     * 动态客户端注册
     */
    static registerClient(req, res) {
        try {
            const registration = req.body;
            // 验证必需字段
            if (!registration.redirect_uris || !Array.isArray(registration.redirect_uris) || registration.redirect_uris.length === 0) {
                res.status(400).json({
                    error: 'invalid_request',
                    error_description: 'redirect_uris is required and must be a non-empty array'
                });
                return;
            }
            // 验证重定向 URI
            for (const uri of registration.redirect_uris) {
                if (!config_1.Config.isValidRedirectUri(uri)) {
                    res.status(400).json({
                        error: 'invalid_redirect_uri',
                        error_description: `Invalid redirect URI: ${uri}`
                    });
                    return;
                }
            }
            // 生成客户端信息
            const clientInfo = {
                client_id: jwt_utils_1.JWTUtils.generateClientId({
                    redirect_uris: registration.redirect_uris,
                    client_name: registration.client_name || undefined,
                    issued_at: Math.floor(Date.now() / 1000)
                }),
                redirect_uris: registration.redirect_uris,
                grant_types: registration.grant_types || ['authorization_code'],
                response_types: registration.response_types || ['code'],
                scope: registration.scope || config_1.Config.SUPPORTED_SCOPES.join(' '),
                token_endpoint_auth_method: registration.token_endpoint_auth_method || 'none',
                client_id_issued_at: Math.floor(Date.now() / 1000)
            };
            console.log(`✅ Client registered: ${registration.client_name || 'Unknown'}`);
            res.status(201).json(clientInfo);
        }
        catch (error) {
            console.error('Client registration error:', error);
            res.status(500).json({
                error: 'server_error',
                error_description: 'Internal server error during client registration'
            });
        }
    }
    /**
     * 处理授权请求
     */
    static handleAuthorizeRequest(req, res) {
        try {
            const { response_type, client_id, redirect_uri, scope, state, code_challenge, code_challenge_method } = req.query;
            // 验证必需参数
            if (!response_type || !client_id || !redirect_uri) {
                res.status(400).json({
                    error: 'invalid_request',
                    error_description: 'Missing required parameters'
                });
                return;
            }
            // 验证响应类型
            if (response_type !== 'code') {
                res.status(400).json({
                    error: 'unsupported_response_type',
                    error_description: 'Only authorization code flow is supported'
                });
                return;
            }
            // 验证 PKCE
            if (!code_challenge || !code_challenge_method) {
                res.status(400).json({
                    error: 'invalid_request',
                    error_description: 'PKCE parameters are required'
                });
                return;
            }
            if (code_challenge_method !== 'S256') {
                res.status(400).json({
                    error: 'invalid_request',
                    error_description: 'Only S256 code challenge method is supported'
                });
                return;
            }
            // 验证客户端 ID
            try {
                jwt_utils_1.JWTUtils.verifyClientId(client_id);
            }
            catch (error) {
                res.status(400).json({
                    error: 'invalid_client',
                    error_description: 'Invalid client_id'
                });
                return;
            }
            // 验证重定向 URI
            if (!config_1.Config.isValidRedirectUri(redirect_uri)) {
                res.status(400).json({
                    error: 'invalid_request',
                    error_description: 'Invalid redirect_uri'
                });
                return;
            }
            // 检查用户是否已登录
            if (!req.session?.user) {
                // 重定向到登录页面
                const loginUrl = `/login?${new URLSearchParams({
                    response_type,
                    client_id,
                    redirect_uri,
                    scope: scope || '',
                    state: state || '',
                    code_challenge,
                    code_challenge_method
                }).toString()}`;
                res.redirect(loginUrl);
                return;
            }
            // 显示授权确认页面
            const authorizeUrl = `/authorize.html?${new URLSearchParams({
                response_type,
                client_id,
                redirect_uri,
                scope: scope || '',
                state: state || '',
                code_challenge,
                code_challenge_method,
                client_name: 'MCP Client' // 可以从客户端信息中获取
            }).toString()}`;
            res.redirect(authorizeUrl);
        }
        catch (error) {
            console.error('Authorization request error:', error);
            res.status(500).json({
                error: 'server_error',
                error_description: 'Internal server error'
            });
        }
    }
    /**
     * 处理授权确认
     */
    static handleAuthorizeConfirmation(req, res) {
        try {
            const { client_id, redirect_uri, state, code_challenge, code_challenge_method, response_type } = req.body;
            let scopes = req.body.scopes || [];
            const user = req.session?.user;
            if (!user) {
                res.status(401).json({
                    error: 'access_denied',
                    error_description: 'User not authenticated'
                });
                return;
            }
            // 确保 scopes 是数组
            if (typeof scopes === 'string') {
                scopes = [scopes];
            }
            console.log(`📝 Received scopes from user ${user}:`, scopes);
            // 验证用户权限
            const validScopes = user_manager_1.UserManager.validateScopes(scopes, user);
            console.log(`✅ Valid scopes for user ${user}:`, validScopes);
            if (validScopes.length === 0) {
                res.status(400).json({
                    error: 'invalid_scope',
                    error_description: 'No valid scopes requested'
                });
                return;
            }
            // 生成授权码
            const authCode = jwt_utils_1.JWTUtils.generateAuthorizationCode({
                client_id,
                code_challenge,
                redirect_uri,
                user,
                scopes: validScopes
            });
            // 构建重定向 URL
            const redirectUrl = new URL(redirect_uri);
            redirectUrl.searchParams.set('code', authCode);
            if (state) {
                redirectUrl.searchParams.set('state', state);
            }
            console.log(`✅ Authorization granted for user ${user} with scopes: ${validScopes.join(', ')}`);
            res.redirect(redirectUrl.toString());
        }
        catch (error) {
            console.error('Authorization confirmation error:', error);
            res.status(500).json({
                error: 'server_error',
                error_description: 'Internal server error'
            });
        }
    }
    /**
     * 处理令牌请求
     */
    static handleTokenRequest(req, res) {
        try {
            const { grant_type, code, redirect_uri, client_id, code_verifier } = req.body;
            // 验证必需参数
            if (!grant_type || !code || !redirect_uri || !client_id || !code_verifier) {
                res.status(400).json({
                    error: 'invalid_request',
                    error_description: 'Missing required parameters'
                });
                return;
            }
            // 验证授权类型
            if (grant_type !== 'authorization_code') {
                res.status(400).json({
                    error: 'unsupported_grant_type',
                    error_description: 'Only authorization_code grant type is supported'
                });
                return;
            }
            // 验证授权码
            let authCodeData;
            try {
                authCodeData = jwt_utils_1.JWTUtils.verifyAuthorizationCode(code);
            }
            catch (error) {
                res.status(400).json({
                    error: 'invalid_grant',
                    error_description: 'Invalid or expired authorization code'
                });
                return;
            }
            // 验证客户端 ID
            if (authCodeData.client_id !== client_id) {
                res.status(400).json({
                    error: 'invalid_grant',
                    error_description: 'Client ID mismatch'
                });
                return;
            }
            // 验证重定向 URI
            if (authCodeData.redirect_uri !== redirect_uri) {
                res.status(400).json({
                    error: 'invalid_grant',
                    error_description: 'Redirect URI mismatch'
                });
                return;
            }
            // 验证 PKCE
            if (!jwt_utils_1.JWTUtils.verifyPKCE(code_verifier, authCodeData.code_challenge)) {
                res.status(400).json({
                    error: 'invalid_grant',
                    error_description: 'PKCE verification failed'
                });
                return;
            }
            // 生成访问令牌
            const accessToken = jwt_utils_1.JWTUtils.generateAccessToken({
                sub: authCodeData.user,
                aud: client_id,
                scope: authCodeData.scopes.join(' ')
            });
            console.log(`✅ Access token issued for user ${authCodeData.user}`);
            res.json({
                access_token: accessToken,
                token_type: 'Bearer',
                expires_in: config_1.Config.ACCESS_TOKEN_EXPIRES_IN,
                scope: authCodeData.scopes.join(' ')
            });
        }
        catch (error) {
            console.error('Token request error:', error);
            res.status(500).json({
                error: 'server_error',
                error_description: 'Internal server error'
            });
        }
    }
}
exports.OAuthHandler = OAuthHandler;
//# sourceMappingURL=oauth-handler.js.map