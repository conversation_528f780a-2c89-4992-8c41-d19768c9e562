"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AuthServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const path_1 = __importDefault(require("path"));
const express_session_1 = __importDefault(require("express-session"));
const config_1 = require("./config");
const user_manager_1 = require("./user-manager");
const oauth_handler_1 = require("./oauth-handler");
class AuthServer {
    constructor() {
        this.app = (0, express_1.default)();
        this.setupMiddleware();
        this.setupRoutes();
    }
    setupMiddleware() {
        // 安全中间件
        this.app.use((0, helmet_1.default)({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'"],
                    scriptSrc: ["'self'", "'unsafe-inline'"],
                    imgSrc: ["'self'", "data:", "https:"],
                },
            },
        }));
        // CORS 配置
        this.app.use((0, cors_1.default)({
            origin: config_1.Config.IS_DEVELOPMENT ? true : [config_1.Config.MCP_SERVER_URL],
            credentials: true
        }));
        // 解析 JSON 和 URL 编码数据
        this.app.use(express_1.default.json());
        this.app.use(express_1.default.urlencoded({ extended: true }));
        // Session 配置
        this.app.use((0, express_session_1.default)({
            secret: config_1.Config.JWT_SECRET,
            resave: false,
            saveUninitialized: false,
            cookie: {
                secure: !config_1.Config.IS_DEVELOPMENT,
                httpOnly: true,
                maxAge: 24 * 60 * 60 * 1000 // 24 小时
            }
        }));
        // 静态文件服务
        this.app.use(express_1.default.static(path_1.default.join(__dirname, '../public')));
    }
    setupRoutes() {
        // OAuth 2.1 元数据端点
        this.app.get('/.well-known/oauth-authorization-server', (req, res) => {
            res.json(oauth_handler_1.OAuthHandler.getAuthServerMetadata());
        });
        // 动态客户端注册端点
        this.app.post('/oauth/register', (req, res) => {
            oauth_handler_1.OAuthHandler.registerClient(req, res);
        });
        // 授权端点
        this.app.get('/oauth/authorize', (req, res) => {
            oauth_handler_1.OAuthHandler.handleAuthorizeRequest(req, res);
        });
        // 授权确认处理
        this.app.post('/oauth/authorize', (req, res) => {
            oauth_handler_1.OAuthHandler.handleAuthorizeConfirmation(req, res);
        });
        // 令牌端点
        this.app.post('/oauth/token', (req, res) => {
            oauth_handler_1.OAuthHandler.handleTokenRequest(req, res);
        });
        // 用户登录页面
        this.app.get('/login', (req, res) => {
            // 保存授权参数到 session
            const { response_type, client_id, redirect_uri, scope, state, code_challenge, code_challenge_method } = req.query;
            if (response_type && client_id && redirect_uri) {
                req.session.authParams = {
                    response_type,
                    client_id,
                    redirect_uri,
                    scope: scope || '',
                    state: state || '',
                    code_challenge: code_challenge || '',
                    code_challenge_method: code_challenge_method || ''
                };
            }
            res.sendFile(path_1.default.join(__dirname, '../public/login.html'));
        });
        // 用户登录处理
        this.app.post('/login', (req, res) => {
            this.handleLogin(req, res);
        });
        // 用户登出
        this.app.post('/logout', (req, res) => {
            req.session.destroy((err) => {
                if (err) {
                    console.error('Logout error:', err);
                    res.status(500).json({ error: 'Logout failed' });
                }
                else {
                    res.json({ message: 'Logged out successfully' });
                }
            });
        });
        // 健康检查端点
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                service: 'auth-server'
            });
        });
        // 错误处理中间件
        this.app.use((err, req, res, next) => {
            console.error('Auth server error:', err);
            res.status(500).json({
                error: 'server_error',
                error_description: 'Internal server error'
            });
        });
        // 404 处理
        this.app.use((req, res) => {
            res.status(404).json({
                error: 'not_found',
                error_description: 'Endpoint not found'
            });
        });
    }
    handleLogin(req, res) {
        try {
            const { username, password } = req.body;
            if (!username || !password) {
                res.redirect('/login?error=' + encodeURIComponent('请输入用户名和密码'));
                return;
            }
            // 验证用户凭据
            const user = user_manager_1.UserManager.authenticateUser(username, password);
            if (!user) {
                res.redirect('/login?error=' + encodeURIComponent('用户名或密码错误，或用户未在白名单中'));
                return;
            }
            // 设置 session
            req.session.user = username;
            // 检查是否有待处理的授权请求
            const authParams = req.session.authParams;
            if (authParams && authParams.response_type && authParams.client_id && authParams.redirect_uri) {
                // 重定向到授权确认页面
                const authorizeUrl = `/authorize.html?${new URLSearchParams({
                    response_type: authParams.response_type,
                    client_id: authParams.client_id,
                    redirect_uri: authParams.redirect_uri,
                    scope: authParams.scope || '',
                    state: authParams.state || '',
                    code_challenge: authParams.code_challenge || '',
                    code_challenge_method: authParams.code_challenge_method || '',
                    client_name: 'MCP Client'
                }).toString()}`;
                // 清除 session 中的授权参数
                delete req.session.authParams;
                res.redirect(authorizeUrl);
            }
            else {
                // 登录成功，显示成功页面或重定向
                res.send(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>登录成功</title>
            <meta charset="UTF-8">
            <style>
              body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
              .success { color: green; }
            </style>
          </head>
          <body>
            <h1 class="success">登录成功！</h1>
            <p>欢迎，${username}！</p>
            <p>您已成功登录 MCP OAuth 授权服务器。</p>
          </body>
          </html>
        `);
            }
        }
        catch (error) {
            console.error('Login error:', error);
            res.redirect('/login?error=' + encodeURIComponent('登录过程中发生错误'));
        }
    }
    async start() {
        return new Promise((resolve, reject) => {
            try {
                this.server = this.app.listen(config_1.Config.AUTH_SERVER_PORT, () => {
                    console.log(`🔐 Auth Server listening on port ${config_1.Config.AUTH_SERVER_PORT}`);
                    resolve();
                });
                this.server.on('error', (error) => {
                    if (error.code === 'EADDRINUSE') {
                        reject(new Error(`Port ${config_1.Config.AUTH_SERVER_PORT} is already in use`));
                    }
                    else {
                        reject(error);
                    }
                });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async stop() {
        return new Promise((resolve) => {
            if (this.server) {
                this.server.close(() => {
                    console.log('🔐 Auth Server stopped');
                    resolve();
                });
            }
            else {
                resolve();
            }
        });
    }
}
exports.AuthServer = AuthServer;
//# sourceMappingURL=auth-server.js.map