export declare class MCPStreamableHTTPServer {
    private app;
    private server;
    private transports;
    constructor();
    private createMCPServer;
    private hasScope;
    private setupMiddleware;
    private setupRoutes;
    private handleMCPRequest;
    private generateSessionId;
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=mcp-streamable-http-server.d.ts.map