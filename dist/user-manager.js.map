{"version": 3, "file": "user-manager.js", "sourceRoot": "", "sources": ["../src/user-manager.ts"], "names": [], "mappings": ";;;AAAA,qCAAkC;AAGlC,MAAa,WAAW;IAGtB;;OAEG;IACH,MAAM,CAAC,UAAU;QACf,MAAM,eAAe,GAAG,eAAM,CAAC,kBAAkB,EAAE,CAAC;QAEpD,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;YACnC,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;QAC7C,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,eAAe,WAAW,CAAC,KAAK,CAAC,IAAI,mBAAmB,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAgB,EAAE,QAAgB;QACxD,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE7C,IAAI,CAAC,IAAI,EAAE,CAAC;YACV,OAAO,CAAC,GAAG,CAAC,+BAA+B,QAAQ,yBAAyB,CAAC,CAAC;YAC9E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,IAAI,CAAC,QAAQ,KAAK,QAAQ,EAAE,CAAC;YAC/B,OAAO,CAAC,GAAG,CAAC,oDAAoD,QAAQ,EAAE,CAAC,CAAC;YAC5E,OAAO,IAAI,CAAC;QACd,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,sCAAsC,QAAQ,EAAE,CAAC,CAAC;QAC9D,OAAO,IAAI,CAAC;IACd,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,gBAAgB,CAAC,QAAgB;QACtC,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;IACzC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,OAAO,CAAC,QAAgB;QAC7B,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC;IACjD,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,aAAa,CAAC,QAAgB;QACnC,MAAM,IAAI,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC;IACjC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,QAAQ,CAAC,QAAgB,EAAE,KAAa;QAC7C,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACvD,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,eAAyB,EAAE,QAAgB;QAC/D,MAAM,UAAU,GAAG,WAAW,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QACvD,MAAM,WAAW,GAAa,EAAE,CAAC;QAEjC,KAAK,MAAM,KAAK,IAAI,eAAe,EAAE,CAAC;YACpC,IAAI,eAAM,CAAC,gBAAgB,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC1E,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1B,CAAC;QACH,CAAC;QAED,OAAO,WAAW,CAAC;IACrB,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,WAAW;QAChB,OAAO,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;IAC9C,CAAC;;AAvFH,kCAwFC;AAvFgB,iBAAK,GAAsB,IAAI,GAAG,EAAE,CAAC"}