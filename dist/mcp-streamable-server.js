"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPStreamableServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const config_1 = require("./config");
const jwt_utils_1 = require("./jwt-utils");
const oauth_handler_1 = require("./oauth-handler");
const mcp_sdk_server_1 = require("./mcp-sdk-server");
class MCPStreamableServer {
    constructor() {
        this.app = (0, express_1.default)();
        this.mcpSDKServer = new mcp_sdk_server_1.MCPSDKServer();
        this.setupMiddleware();
        this.setupRoutes();
    }
    setupMiddleware() {
        // 安全中间件 - 在开发环境中禁用 CSP
        if (config_1.Config.IS_DEVELOPMENT) {
            this.app.use((0, helmet_1.default)({
                contentSecurityPolicy: false
            }));
        }
        else {
            this.app.use((0, helmet_1.default)());
        }
        // CORS 配置 - 允许 MCP Inspector 连接
        this.app.use((0, cors_1.default)({
            origin: true,
            credentials: true,
            methods: ['GET', 'POST', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
        }));
        // 解析 JSON
        this.app.use(express_1.default.json());
        // Bearer Token 验证中间件（仅对 /mcp 路径）
        this.app.use('/mcp', this.authenticateToken.bind(this));
    }
    authenticateToken(req, res, next) {
        const authHeader = req.headers.authorization;
        const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN
        if (!token) {
            res.status(401).set({
                'WWW-Authenticate': `Bearer realm="mcp", as_uri="${config_1.Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
            }).json({
                error: 'invalid_token',
                error_description: 'Missing access token'
            });
            return;
        }
        try {
            const decoded = jwt_utils_1.JWTUtils.verifyAccessToken(token);
            req.user = decoded;
            // 设置访问令牌到 SDK 服务器
            this.mcpSDKServer.setAccessToken(token);
            next();
        }
        catch (error) {
            res.status(401).set({
                'WWW-Authenticate': `Bearer realm="mcp", as_uri="${config_1.Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
            }).json({
                error: 'invalid_token',
                error_description: 'Invalid or expired access token'
            });
        }
    }
    setupRoutes() {
        // 资源服务器元数据端点
        this.app.get('/.well-known/oauth-protected-resource', (req, res) => {
            res.json(oauth_handler_1.OAuthHandler.getResourceServerMetadata());
        });
        // MCP Streamable HTTP 传输端点
        this.app.post('/mcp', (req, res) => {
            console.log('📨 Received MCP request');
            this.handleStreamableHTTP(req, res);
        });
        // 健康检查端点
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                service: 'mcp-streamable-server'
            });
        });
        // 错误处理中间件
        this.app.use((err, req, res, next) => {
            console.error('MCP streamable server error:', err);
            res.status(500).json({
                error: 'server_error',
                error_description: 'Internal server error'
            });
        });
        // 404 处理
        this.app.use((req, res) => {
            res.status(404).json({
                error: 'not_found',
                error_description: 'Endpoint not found'
            });
        });
    }
    async handleStreamableHTTP(req, res) {
        try {
            const user = req.user;
            console.log(`📨 Streamable HTTP request from ${user.sub}`);
            // 设置正确的 JSON-RPC over HTTP 头部
            res.setHeader('Content-Type', 'application/json');
            res.setHeader('Cache-Control', 'no-cache');
            res.setHeader('Access-Control-Allow-Origin', '*');
            res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
            res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
            // 处理请求体中的消息
            const message = req.body;
            console.log('Request body:', JSON.stringify(message, null, 2));
            // 处理单个 JSON-RPC 消息
            const response = await this.processMessage(message, user);
            console.log('Sending response...');
            res.status(200).json(response);
            console.log('Response sent!');
        }
        catch (error) {
            console.error('Streamable HTTP error:', error);
            res.status(500).json({
                jsonrpc: '2.0',
                id: null,
                error: {
                    code: -32603,
                    message: 'Internal error',
                    data: String(error)
                }
            });
        }
    }
    async processMessage(message, user) {
        try {
            // 验证消息格式
            if (!message.jsonrpc || message.jsonrpc !== '2.0') {
                return {
                    jsonrpc: '2.0',
                    id: message.id || null,
                    error: {
                        code: -32600,
                        message: 'Invalid Request',
                        data: 'Missing or invalid jsonrpc field'
                    }
                };
            }
            // 处理不同的 MCP 方法
            switch (message.method) {
                case 'initialize':
                    return {
                        jsonrpc: '2.0',
                        id: message.id,
                        result: {
                            protocolVersion: '2024-11-05',
                            capabilities: {
                                tools: {},
                                resources: {},
                                prompts: {}
                            },
                            serverInfo: {
                                name: 'MCP OAuth Server',
                                version: '1.0.0'
                            }
                        }
                    };
                case 'tools/list':
                    return await this.handleToolsList(message, user);
                case 'tools/call':
                    return await this.handleToolsCall(message, user);
                case 'resources/list':
                    return await this.handleResourcesList(message, user);
                case 'resources/read':
                    return await this.handleResourcesRead(message, user);
                case 'prompts/list':
                    return await this.handlePromptsList(message, user);
                case 'prompts/get':
                    return await this.handlePromptsGet(message, user);
                default:
                    return {
                        jsonrpc: '2.0',
                        id: message.id || null,
                        error: {
                            code: -32601,
                            message: 'Method not found',
                            data: { method: message.method }
                        }
                    };
            }
        }
        catch (error) {
            console.error('Message processing error:', error);
            return {
                jsonrpc: '2.0',
                id: message.id || null,
                error: {
                    code: -32603,
                    message: 'Internal error',
                    data: String(error)
                }
            };
        }
    }
    async handleToolsList(message, user) {
        const userScopes = user.scope.split(' ');
        const tools = [
            {
                name: 'echo',
                description: 'Echo back the input message',
                inputSchema: {
                    type: 'object',
                    properties: {
                        message: {
                            type: 'string',
                            description: 'Message to echo back'
                        }
                    },
                    required: ['message']
                }
            }
        ];
        // 根据权限添加工具
        if (userScopes.includes('read')) {
            tools.push({
                name: 'get_user_info',
                description: 'Get current user information',
                inputSchema: {
                    type: 'object',
                    properties: {},
                    required: []
                }
            });
        }
        if (userScopes.includes('execute')) {
            tools.push({
                name: 'calculator',
                description: 'Perform basic arithmetic operations',
                inputSchema: {
                    type: 'object',
                    properties: {
                        operation: {
                            type: 'string',
                            enum: ['add', 'subtract', 'multiply', 'divide'],
                            description: 'The arithmetic operation to perform'
                        },
                        a: {
                            type: 'number',
                            description: 'First number'
                        },
                        b: {
                            type: 'number',
                            description: 'Second number'
                        }
                    },
                    required: ['operation', 'a', 'b']
                }
            });
        }
        if (userScopes.includes('admin')) {
            tools.push({
                name: 'system_info',
                description: 'Get system information',
                inputSchema: {
                    type: 'object',
                    properties: {},
                    required: []
                }
            });
        }
        return {
            jsonrpc: '2.0',
            id: message.id,
            result: {
                tools: tools
            }
        };
    }
    async handleToolsCall(message, user) {
        const { name, arguments: args } = message.params;
        const userScopes = user.scope.split(' ');
        switch (name) {
            case 'echo':
                return {
                    jsonrpc: '2.0',
                    id: message.id,
                    result: {
                        content: [
                            {
                                type: 'text',
                                text: `Echo: ${args.message}`
                            }
                        ]
                    }
                };
            case 'get_user_info':
                if (!userScopes.includes('read')) {
                    return {
                        jsonrpc: '2.0',
                        id: message.id,
                        error: {
                            code: -32603,
                            message: 'Insufficient permissions: read scope required'
                        }
                    };
                }
                return {
                    jsonrpc: '2.0',
                    id: message.id,
                    result: {
                        content: [
                            {
                                type: 'text',
                                text: JSON.stringify({
                                    user: user.sub,
                                    scopes: userScopes,
                                    client: user.aud,
                                    expires: new Date(user.exp * 1000).toISOString()
                                }, null, 2)
                            }
                        ]
                    }
                };
            case 'calculator':
                if (!userScopes.includes('execute')) {
                    return {
                        jsonrpc: '2.0',
                        id: message.id,
                        error: {
                            code: -32603,
                            message: 'Insufficient permissions: execute scope required'
                        }
                    };
                }
                const { operation, a, b } = args;
                let result;
                switch (operation) {
                    case 'add':
                        result = a + b;
                        break;
                    case 'subtract':
                        result = a - b;
                        break;
                    case 'multiply':
                        result = a * b;
                        break;
                    case 'divide':
                        if (b === 0) {
                            return {
                                jsonrpc: '2.0',
                                id: message.id,
                                error: {
                                    code: -32602,
                                    message: 'Division by zero is not allowed'
                                }
                            };
                        }
                        result = a / b;
                        break;
                    default:
                        return {
                            jsonrpc: '2.0',
                            id: message.id,
                            error: {
                                code: -32602,
                                message: `Unknown operation: ${operation}`
                            }
                        };
                }
                return {
                    jsonrpc: '2.0',
                    id: message.id,
                    result: {
                        content: [
                            {
                                type: 'text',
                                text: `${a} ${operation} ${b} = ${result}`
                            }
                        ]
                    }
                };
            default:
                return {
                    jsonrpc: '2.0',
                    id: message.id,
                    error: {
                        code: -32601,
                        message: 'Tool not found',
                        data: { tool: name }
                    }
                };
        }
    }
    async handleResourcesList(message, user) {
        return {
            jsonrpc: '2.0',
            id: message.id,
            result: {
                resources: [
                    {
                        uri: 'greeting://world',
                        name: 'Greeting Resource',
                        description: 'A simple greeting resource',
                        mimeType: 'text/plain'
                    }
                ]
            }
        };
    }
    async handleResourcesRead(message, user) {
        const { uri } = message.params;
        if (uri.startsWith('greeting://')) {
            const name = uri.replace('greeting://', '');
            return {
                jsonrpc: '2.0',
                id: message.id,
                result: {
                    contents: [
                        {
                            uri,
                            mimeType: 'text/plain',
                            text: `Hello, ${name}! You are authenticated as ${user.sub}.`
                        }
                    ]
                }
            };
        }
        return {
            jsonrpc: '2.0',
            id: message.id,
            error: {
                code: -32602,
                message: 'Resource not found',
                data: { uri }
            }
        };
    }
    async handlePromptsList(message, user) {
        return {
            jsonrpc: '2.0',
            id: message.id,
            result: {
                prompts: [
                    {
                        name: 'user_assistant',
                        description: 'A helpful assistant for the authenticated user'
                    }
                ]
            }
        };
    }
    async handlePromptsGet(message, user) {
        const { name } = message.params;
        if (name === 'user_assistant') {
            return {
                jsonrpc: '2.0',
                id: message.id,
                result: {
                    messages: [
                        {
                            role: 'user',
                            content: {
                                type: 'text',
                                text: `You are a helpful assistant for ${user.sub}. The user has permissions: ${user.scope}.`
                            }
                        }
                    ]
                }
            };
        }
        return {
            jsonrpc: '2.0',
            id: message.id,
            error: {
                code: -32601,
                message: 'Prompt not found',
                data: { prompt: name }
            }
        };
    }
    async start() {
        return new Promise((resolve, reject) => {
            try {
                this.server = this.app.listen(config_1.Config.MCP_SERVER_PORT, () => {
                    console.log(`🔧 MCP Streamable Server listening on port ${config_1.Config.MCP_SERVER_PORT}`);
                    resolve();
                });
                this.server.on('error', (error) => {
                    if (error.code === 'EADDRINUSE') {
                        reject(new Error(`Port ${config_1.Config.MCP_SERVER_PORT} is already in use`));
                    }
                    else {
                        reject(error);
                    }
                });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async stop() {
        return new Promise((resolve) => {
            if (this.server) {
                this.server.close(() => {
                    console.log('🔧 MCP Streamable Server stopped');
                    resolve();
                });
            }
            else {
                resolve();
            }
        });
    }
}
exports.MCPStreamableServer = MCPStreamableServer;
//# sourceMappingURL=mcp-streamable-server.js.map