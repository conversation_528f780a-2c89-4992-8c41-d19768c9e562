export declare class MCPStreamableServer {
    private app;
    private server;
    private mcpSDKServer;
    constructor();
    private setupMiddleware;
    private authenticateToken;
    private setupRoutes;
    private handleStreamableHTTP;
    private processMessage;
    private handleToolsList;
    private handleToolsCall;
    private handleResourcesList;
    private handleResourcesRead;
    private handlePromptsList;
    private handlePromptsGet;
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=mcp-streamable-server.d.ts.map