import { Request, Response } from 'express';
import { AuthServerMetadata, ResourceServerMetadata } from './types';
export declare class OAuthHandler {
    /**
     * 获取授权服务器元数据
     */
    static getAuthServerMetadata(): AuthServerMetadata;
    /**
     * 获取资源服务器元数据
     */
    static getResourceServerMetadata(): ResourceServerMetadata;
    /**
     * 动态客户端注册
     */
    static registerClient(req: Request, res: Response): void;
    /**
     * 处理授权请求
     */
    static handleAuthorizeRequest(req: Request, res: Response): void;
    /**
     * 处理授权确认
     */
    static handleAuthorizeConfirmation(req: Request, res: Response): void;
    /**
     * 处理令牌请求
     */
    static handleTokenRequest(req: Request, res: Response): void;
}
//# sourceMappingURL=oauth-handler.d.ts.map