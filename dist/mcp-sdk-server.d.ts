import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
export declare class MCPSDKServer {
    private server;
    private currentUser;
    constructor();
    /**
     * 设置访问令牌（用于权限验证）
     */
    setAccessToken(token: string): void;
    /**
     * 检查用户是否有指定权限
     */
    private hasScope;
    /**
     * 设置 MCP 工具
     */
    private setupTools;
    /**
     * 设置 MCP 资源
     */
    private setupResources;
    /**
     * 设置 MCP 提示
     */
    private setupPrompts;
    /**
     * 启动 MCP 服务器（Stdio 传输）
     */
    startStdio(): Promise<void>;
    /**
     * 获取服务器实例（用于其他传输方式）
     */
    getServer(): McpServer;
}
//# sourceMappingURL=mcp-sdk-server.d.ts.map