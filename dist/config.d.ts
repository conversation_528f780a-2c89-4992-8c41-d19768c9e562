import { User } from './types';
export declare class Config {
    static readonly AUTH_SERVER_PORT: number;
    static readonly MCP_SERVER_PORT: number;
    static readonly AUTH_SERVER_URL: string;
    static readonly MCP_SERVER_URL: string;
    static readonly JWT_SECRET: string;
    static readonly AUTH_CODE_SECRET: string;
    static readonly CLIENT_SECRET: string;
    static readonly NODE_ENV: string;
    static readonly IS_DEVELOPMENT: boolean;
    static readonly ACCESS_TOKEN_EXPIRES_IN = 3600;
    static readonly AUTH_CODE_EXPIRES_IN = 600;
    static readonly SUPPORTED_SCOPES: string[];
    static getAuthorizedUsers(): User[];
    static validateConfig(): void;
    static isValidRedirectUri(uri: string): boolean;
}
//# sourceMappingURL=config.d.ts.map