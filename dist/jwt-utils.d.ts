import { AuthorizationCode, AccessToken } from './types';
export declare class JWTUtils {
    /**
     * 生成授权码 JWT
     */
    static generateAuthorizationCode(payload: Omit<AuthorizationCode, 'exp'>): string;
    /**
     * 验证并解析授权码 JWT
     */
    static verifyAuthorizationCode(code: string): AuthorizationCode;
    /**
     * 生成访问令牌 JWT
     */
    static generateAccessToken(payload: Omit<AccessToken, 'exp'>): string;
    /**
     * 验证并解析访问令牌 JWT
     */
    static verifyAccessToken(token: string): AccessToken;
    /**
     * 生成客户端 ID（自签名 JWT）
     */
    static generateClientId(clientInfo: {
        redirect_uris: string[];
        client_name?: string | undefined;
        issued_at: number;
    }): string;
    /**
     * 验证客户端 ID
     */
    static verifyClientId(clientId: string): any;
    /**
     * 验证 PKCE code_verifier
     */
    static verifyPKCE(codeVerifier: string, codeChallenge: string, method?: string): boolean;
}
//# sourceMappingURL=jwt-utils.d.ts.map