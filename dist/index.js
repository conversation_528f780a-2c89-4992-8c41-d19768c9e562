"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const config_1 = require("./config");
const user_manager_1 = require("./user-manager");
const auth_server_1 = require("./auth-server");
const mcp_streamable_http_server_1 = require("./mcp-streamable-http-server");
async function main() {
    try {
        // 验证配置
        config_1.Config.validateConfig();
        console.log('✅ Configuration validated');
        // 初始化用户管理器
        user_manager_1.UserManager.initialize();
        console.log('✅ User manager initialized');
        // 启动授权服务器
        const authServer = new auth_server_1.AuthServer();
        await authServer.start();
        console.log(`✅ Auth Server started on port ${config_1.Config.AUTH_SERVER_PORT}`);
        // 启动 MCP Streamable HTTP 服务器
        const mcpServer = new mcp_streamable_http_server_1.MCPStreamableHTTPServer();
        await mcpServer.start();
        console.log(`✅ MCP Streamable HTTP Server started on port ${config_1.Config.MCP_SERVER_PORT}`);
        console.log('\n🚀 MCP OAuth Server is running!');
        console.log(`📋 Auth Server: ${config_1.Config.AUTH_SERVER_URL}`);
        console.log(`🔧 MCP Server: ${config_1.Config.MCP_SERVER_URL}`);
        console.log(`👥 Authorized users: ${user_manager_1.UserManager.getAllUsers().join(', ')}`);
        // 优雅关闭处理
        process.on('SIGINT', async () => {
            console.log('\n🛑 Shutting down servers...');
            await authServer.stop();
            await mcpServer.stop();
            process.exit(0);
        });
    }
    catch (error) {
        console.error('❌ Failed to start servers:', error);
        process.exit(1);
    }
}
// 启动应用
main().catch(console.error);
//# sourceMappingURL=index.js.map