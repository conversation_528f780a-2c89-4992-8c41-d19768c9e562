"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.JWTUtils = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const config_1 = require("./config");
class JWTUtils {
    /**
     * 生成授权码 JWT
     */
    static generateAuthorizationCode(payload) {
        const authCode = {
            ...payload,
            exp: Math.floor(Date.now() / 1000) + config_1.Config.AUTH_CODE_EXPIRES_IN
        };
        return jsonwebtoken_1.default.sign(authCode, config_1.Config.AUTH_CODE_SECRET, {
            algorithm: 'HS256'
        });
    }
    /**
     * 验证并解析授权码 JWT
     */
    static verifyAuthorizationCode(code) {
        try {
            const decoded = jsonwebtoken_1.default.verify(code, config_1.Config.AUTH_CODE_SECRET);
            // 检查是否过期
            if (decoded.exp < Math.floor(Date.now() / 1000)) {
                throw new Error('Authorization code expired');
            }
            return decoded;
        }
        catch (error) {
            throw new Error(`Invalid authorization code: ${error}`);
        }
    }
    /**
     * 生成访问令牌 JWT
     */
    static generateAccessToken(payload) {
        const accessToken = {
            ...payload,
            exp: Math.floor(Date.now() / 1000) + config_1.Config.ACCESS_TOKEN_EXPIRES_IN
        };
        return jsonwebtoken_1.default.sign(accessToken, config_1.Config.JWT_SECRET, {
            algorithm: 'HS256'
        });
    }
    /**
     * 验证并解析访问令牌 JWT
     */
    static verifyAccessToken(token) {
        try {
            const decoded = jsonwebtoken_1.default.verify(token, config_1.Config.JWT_SECRET);
            // 检查是否过期
            if (decoded.exp < Math.floor(Date.now() / 1000)) {
                throw new Error('Access token expired');
            }
            return decoded;
        }
        catch (error) {
            throw new Error(`Invalid access token: ${error}`);
        }
    }
    /**
     * 生成客户端 ID（自签名 JWT）
     */
    static generateClientId(clientInfo) {
        return jsonwebtoken_1.default.sign(clientInfo, config_1.Config.CLIENT_SECRET, {
            algorithm: 'HS256'
        });
    }
    /**
     * 验证客户端 ID
     */
    static verifyClientId(clientId) {
        try {
            return jsonwebtoken_1.default.verify(clientId, config_1.Config.CLIENT_SECRET);
        }
        catch (error) {
            throw new Error(`Invalid client ID: ${error}`);
        }
    }
    /**
     * 验证 PKCE code_verifier
     */
    static verifyPKCE(codeVerifier, codeChallenge, method = 'S256') {
        if (method !== 'S256') {
            throw new Error('Only S256 code challenge method is supported');
        }
        const crypto = require('crypto');
        const hash = crypto.createHash('sha256').update(codeVerifier).digest();
        const base64Hash = hash.toString('base64url');
        return base64Hash === codeChallenge;
    }
}
exports.JWTUtils = JWTUtils;
//# sourceMappingURL=jwt-utils.js.map