"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UserManager = void 0;
const config_1 = require("./config");
class UserManager {
    /**
     * 初始化用户管理器
     */
    static initialize() {
        const authorizedUsers = config_1.Config.getAuthorizedUsers();
        for (const user of authorizedUsers) {
            UserManager.users.set(user.username, user);
        }
        console.log(`Initialized ${UserManager.users.size} authorized users`);
    }
    /**
     * 验证用户凭据
     */
    static authenticateUser(username, password) {
        const user = UserManager.users.get(username);
        if (!user) {
            console.log(`Authentication failed: User ${username} not found in whitelist`);
            return null;
        }
        if (user.password !== password) {
            console.log(`Authentication failed: Invalid password for user ${username}`);
            return null;
        }
        console.log(`Authentication successful for user ${username}`);
        return user;
    }
    /**
     * 检查用户是否在白名单中
     */
    static isUserAuthorized(username) {
        return UserManager.users.has(username);
    }
    /**
     * 获取用户信息
     */
    static getUser(username) {
        return UserManager.users.get(username) || null;
    }
    /**
     * 获取用户的权限范围
     */
    static getUserScopes(username) {
        const user = UserManager.users.get(username);
        return user ? user.scopes : [];
    }
    /**
     * 验证用户是否有特定权限
     */
    static hasScope(username, scope) {
        const userScopes = UserManager.getUserScopes(username);
        return userScopes.includes(scope);
    }
    /**
     * 验证请求的权限范围是否有效
     */
    static validateScopes(requestedScopes, username) {
        const userScopes = UserManager.getUserScopes(username);
        const validScopes = [];
        for (const scope of requestedScopes) {
            if (config_1.Config.SUPPORTED_SCOPES.includes(scope) && userScopes.includes(scope)) {
                validScopes.push(scope);
            }
        }
        return validScopes;
    }
    /**
     * 获取所有授权用户列表（用于调试）
     */
    static getAllUsers() {
        return Array.from(UserManager.users.keys());
    }
}
exports.UserManager = UserManager;
UserManager.users = new Map();
//# sourceMappingURL=user-manager.js.map