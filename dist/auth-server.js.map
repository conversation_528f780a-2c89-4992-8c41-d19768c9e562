{"version": 3, "file": "auth-server.js", "sourceRoot": "", "sources": ["../src/auth-server.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA8D;AAC9D,gDAAwB;AACxB,oDAA4B;AAC5B,gDAAwB;AACxB,sEAAsC;AACtC,qCAAkC;AAClC,iDAA6C;AAC7C,mDAA+C;AAkB/C,MAAa,UAAU;IAIrB;QACE,IAAI,CAAC,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;QACrB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,eAAe;QACrB,6BAA6B;QAC7B,IAAI,eAAM,CAAC,cAAc,EAAE,CAAC;YAC1B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,EAAC;gBAClB,qBAAqB,EAAE,KAAK;aAC7B,CAAC,CAAC,CAAC;QACN,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,gBAAM,EAAC;gBAClB,qBAAqB,EAAE;oBACrB,UAAU,EAAE;wBACV,UAAU,EAAE,CAAC,QAAQ,CAAC;wBACtB,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;wBACvC,SAAS,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;wBACxC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;wBACrC,UAAU,EAAE,CAAC,QAAQ,CAAC;qBACvB;iBACF;aACF,CAAC,CAAC,CAAC;QACN,CAAC;QAED,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC;YAChB,MAAM,EAAE,eAAM,CAAC,cAAc,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,eAAM,CAAC,cAAc,CAAC;YAC9D,WAAW,EAAE,IAAI;SAClB,CAAC,CAAC,CAAC;QAEJ,qBAAqB;QACrB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;QAC7B,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,UAAU,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;QAErD,aAAa;QACb,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,yBAAO,EAAC;YACnB,MAAM,EAAE,eAAM,CAAC,UAAU;YACzB,MAAM,EAAE,KAAK;YACb,iBAAiB,EAAE,KAAK;YACxB,MAAM,EAAE;gBACN,MAAM,EAAE,CAAC,eAAM,CAAC,cAAc;gBAC9B,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,QAAQ;aACrC;SACF,CAAC,CAAC,CAAC;QAEJ,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,MAAM,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC,CAAC,CAAC;IAClE,CAAC;IAEO,WAAW;QACjB,kBAAkB;QAClB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yCAAyC,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACtF,GAAG,CAAC,IAAI,CAAC,4BAAY,CAAC,qBAAqB,EAAE,CAAC,CAAC;QACjD,CAAC,CAAC,CAAC;QAEH,YAAY;QACZ,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YAC/D,4BAAY,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YAC/D,4BAAY,CAAC,sBAAsB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAChD,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YAChE,4BAAY,CAAC,2BAA2B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YAC5D,4BAAY,CAAC,kBAAkB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACrD,kBAAkB;YAClB,MAAM,EACJ,aAAa,EACb,SAAS,EACT,YAAY,EACZ,KAAK,EACL,KAAK,EACL,cAAc,EACd,qBAAqB,EACtB,GAAG,GAAG,CAAC,KAAY,CAAC;YAErB,IAAI,aAAa,IAAI,SAAS,IAAI,YAAY,EAAE,CAAC;gBAC/C,GAAG,CAAC,OAAO,CAAC,UAAU,GAAG;oBACvB,aAAa;oBACb,SAAS;oBACT,YAAY;oBACZ,KAAK,EAAE,KAAK,IAAI,EAAE;oBAClB,KAAK,EAAE,KAAK,IAAI,EAAE;oBAClB,cAAc,EAAE,cAAc,IAAI,EAAE;oBACpC,qBAAqB,EAAE,qBAAqB,IAAI,EAAE;iBACnD,CAAC;YACJ,CAAC;YAED,GAAG,CAAC,QAAQ,CAAC,cAAI,CAAC,IAAI,CAAC,SAAS,EAAE,sBAAsB,CAAC,CAAC,CAAC;QAC7D,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACtD,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO;QACP,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACvD,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,EAAE;gBAC1B,IAAI,GAAG,EAAE,CAAC;oBACR,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC;oBACpC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,eAAe,EAAE,CAAC,CAAC;gBACnD,CAAC;qBAAM,CAAC;oBACN,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,yBAAyB,EAAE,CAAC,CAAC;gBACnD,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACtD,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,aAAa;aACvB,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,GAAY,EAAE,GAAa,EAAE,IAAS,EAAE,EAAE;YAChE,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;YACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,uBAAuB;aAC3C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,WAAW;gBAClB,iBAAiB,EAAE,oBAAoB;aACxC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,WAAW,CAAC,GAAY,EAAE,GAAa;QAC7C,IAAI,CAAC;YACH,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAExC,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAC3B,GAAG,CAAC,QAAQ,CAAC,eAAe,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;gBAChE,OAAO;YACT,CAAC;YAED,SAAS;YACT,MAAM,IAAI,GAAG,0BAAW,CAAC,gBAAgB,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;YAE9D,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,GAAG,CAAC,QAAQ,CAAC,eAAe,GAAG,kBAAkB,CAAC,oBAAoB,CAAC,CAAC,CAAC;gBACzE,OAAO;YACT,CAAC;YAED,aAAa;YACb,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAC;YAE5B,gBAAgB;YAChB,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;YAE1C,IAAI,UAAU,IAAI,UAAU,CAAC,aAAa,IAAI,UAAU,CAAC,SAAS,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC9F,aAAa;gBACb,MAAM,YAAY,GAAG,oBAAoB,IAAI,eAAe,CAAC;oBAC3D,aAAa,EAAE,UAAU,CAAC,aAAa;oBACvC,SAAS,EAAE,UAAU,CAAC,SAAS;oBAC/B,YAAY,EAAE,UAAU,CAAC,YAAY;oBACrC,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE;oBAC7B,KAAK,EAAE,UAAU,CAAC,KAAK,IAAI,EAAE;oBAC7B,cAAc,EAAE,UAAU,CAAC,cAAc,IAAI,EAAE;oBAC/C,qBAAqB,EAAE,UAAU,CAAC,qBAAqB,IAAI,EAAE;oBAC7D,WAAW,EAAE,YAAY;iBAC1B,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;gBAEhB,oBAAoB;gBACpB,OAAO,GAAG,CAAC,OAAO,CAAC,UAAU,CAAC;gBAE9B,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;YAC7B,CAAC;iBAAM,CAAC;gBACN,kBAAkB;gBAClB,GAAG,CAAC,IAAI,CAAC;;;;;;;;;;;;;oBAaG,QAAQ;;;;SAInB,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;YACrC,GAAG,CAAC,QAAQ,CAAC,eAAe,GAAG,kBAAkB,CAAC,WAAW,CAAC,CAAC,CAAC;QAClE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,eAAM,CAAC,gBAAgB,EAAE,GAAG,EAAE;oBAC1D,OAAO,CAAC,GAAG,CAAC,oCAAoC,eAAM,CAAC,gBAAgB,EAAE,CAAC,CAAC;oBAC3E,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE;oBACrC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;wBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,eAAM,CAAC,gBAAgB,oBAAoB,CAAC,CAAC,CAAC;oBACzE,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;oBACrB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;oBACtC,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA9PD,gCA8PC"}