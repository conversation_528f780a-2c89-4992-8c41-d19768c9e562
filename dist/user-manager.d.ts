import { User } from './types';
export declare class UserManager {
    private static users;
    /**
     * 初始化用户管理器
     */
    static initialize(): void;
    /**
     * 验证用户凭据
     */
    static authenticateUser(username: string, password: string): User | null;
    /**
     * 检查用户是否在白名单中
     */
    static isUserAuthorized(username: string): boolean;
    /**
     * 获取用户信息
     */
    static getUser(username: string): User | null;
    /**
     * 获取用户的权限范围
     */
    static getUserScopes(username: string): string[];
    /**
     * 验证用户是否有特定权限
     */
    static hasScope(username: string, scope: string): boolean;
    /**
     * 验证请求的权限范围是否有效
     */
    static validateScopes(requestedScopes: string[], username: string): string[];
    /**
     * 获取所有授权用户列表（用于调试）
     */
    static getAllUsers(): string[];
}
//# sourceMappingURL=user-manager.d.ts.map