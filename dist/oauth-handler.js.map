{"version": 3, "file": "oauth-handler.js", "sourceRoot": "", "sources": ["../src/oauth-handler.ts"], "names": [], "mappings": ";;;AACA,qCAAkC;AAClC,iDAA6C;AAC7C,2CAAuC;AAUvC,MAAa,YAAY;IACvB;;OAEG;IACH,MAAM,CAAC,qBAAqB;QAC1B,OAAO;YACL,MAAM,EAAE,eAAM,CAAC,eAAe;YAC9B,sBAAsB,EAAE,GAAG,eAAM,CAAC,eAAe,kBAAkB;YACnE,cAAc,EAAE,GAAG,eAAM,CAAC,eAAe,cAAc;YACvD,qBAAqB,EAAE,GAAG,eAAM,CAAC,eAAe,iBAAiB;YACjE,gBAAgB,EAAE,eAAM,CAAC,gBAAgB;YACzC,wBAAwB,EAAE,CAAC,MAAM,CAAC;YAClC,qBAAqB,EAAE,CAAC,oBAAoB,CAAC;YAC7C,qCAAqC,EAAE,CAAC,MAAM,CAAC;YAC/C,gCAAgC,EAAE,CAAC,MAAM,CAAC;SAC3C,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,yBAAyB;QAC9B,OAAO;YACL,QAAQ,EAAE,eAAM,CAAC,cAAc;YAC/B,qBAAqB,EAAE,CAAC,eAAM,CAAC,eAAe,CAAC;YAC/C,gBAAgB,EAAE,eAAM,CAAC,gBAAgB;YACzC,wBAAwB,EAAE,CAAC,QAAQ,CAAC;SACrC,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,cAAc,CAAC,GAAY,EAAE,GAAa;QAC/C,IAAI,CAAC;YACH,MAAM,YAAY,GAA4B,GAAG,CAAC,IAAI,CAAC;YAEvD,SAAS;YACT,IAAI,CAAC,YAAY,CAAC,aAAa,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC,IAAI,YAAY,CAAC,aAAa,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACzH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,iBAAiB;oBACxB,iBAAiB,EAAE,yDAAyD;iBAC7E,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,YAAY;YACZ,KAAK,MAAM,GAAG,IAAI,YAAY,CAAC,aAAa,EAAE,CAAC;gBAC7C,IAAI,CAAC,eAAM,CAAC,kBAAkB,CAAC,GAAG,CAAC,EAAE,CAAC;oBACpC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;wBACnB,KAAK,EAAE,sBAAsB;wBAC7B,iBAAiB,EAAE,yBAAyB,GAAG,EAAE;qBAClD,CAAC,CAAC;oBACH,OAAO;gBACT,CAAC;YACH,CAAC;YAED,UAAU;YACV,MAAM,UAAU,GAAoB;gBAClC,SAAS,EAAE,oBAAQ,CAAC,gBAAgB,CAAC;oBACnC,aAAa,EAAE,YAAY,CAAC,aAAa;oBACzC,WAAW,EAAE,YAAY,CAAC,WAAW,IAAI,SAAS;oBAClD,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;iBACzC,CAAC;gBACF,aAAa,EAAE,YAAY,CAAC,aAAa;gBACzC,WAAW,EAAE,YAAY,CAAC,WAAW,IAAI,CAAC,oBAAoB,CAAC;gBAC/D,cAAc,EAAE,YAAY,CAAC,cAAc,IAAI,CAAC,MAAM,CAAC;gBACvD,KAAK,EAAE,YAAY,CAAC,KAAK,IAAI,eAAM,CAAC,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC;gBAC9D,0BAA0B,EAAE,YAAY,CAAC,0BAA0B,IAAI,MAAM;gBAC7E,mBAAmB,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;aACnD,CAAC;YAEF,OAAO,CAAC,GAAG,CAAC,wBAAwB,YAAY,CAAC,WAAW,IAAI,SAAS,EAAE,CAAC,CAAC;YAE7E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACnC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACnD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,kDAAkD;aACtE,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,sBAAsB,CAAC,GAAY,EAAE,GAAa;QACvD,IAAI,CAAC;YACH,MAAM,EACJ,aAAa,EACb,SAAS,EACT,YAAY,EACZ,KAAK,EACL,KAAK,EACL,cAAc,EACd,qBAAqB,EACtB,GAAG,GAAG,CAAC,KAAY,CAAC;YAErB,SAAS;YACT,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,IAAI,CAAC,YAAY,EAAE,CAAC;gBAClD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,iBAAiB;oBACxB,iBAAiB,EAAE,6BAA6B;iBACjD,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,SAAS;YACT,IAAI,aAAa,KAAK,MAAM,EAAE,CAAC;gBAC7B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,2BAA2B;oBAClC,iBAAiB,EAAE,2CAA2C;iBAC/D,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,UAAU;YACV,IAAI,CAAC,cAAc,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAC9C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,iBAAiB;oBACxB,iBAAiB,EAAE,8BAA8B;iBAClD,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,qBAAqB,KAAK,MAAM,EAAE,CAAC;gBACrC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,iBAAiB;oBACxB,iBAAiB,EAAE,8CAA8C;iBAClE,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,WAAW;YACX,IAAI,CAAC;gBACH,oBAAQ,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,gBAAgB;oBACvB,iBAAiB,EAAE,mBAAmB;iBACvC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,YAAY;YACZ,IAAI,CAAC,eAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC;gBAC7C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,iBAAiB;oBACxB,iBAAiB,EAAE,sBAAsB;iBAC1C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,YAAY;YACZ,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,CAAC;gBACvB,WAAW;gBACX,MAAM,QAAQ,GAAG,UAAU,IAAI,eAAe,CAAC;oBAC7C,aAAa;oBACb,SAAS;oBACT,YAAY;oBACZ,KAAK,EAAE,KAAK,IAAI,EAAE;oBAClB,KAAK,EAAE,KAAK,IAAI,EAAE;oBAClB,cAAc;oBACd,qBAAqB;iBACtB,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;gBAEhB,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBACvB,OAAO;YACT,CAAC;YAED,WAAW;YACX,MAAM,YAAY,GAAG,mBAAmB,IAAI,eAAe,CAAC;gBAC1D,aAAa;gBACb,SAAS;gBACT,YAAY;gBACZ,KAAK,EAAE,KAAK,IAAI,EAAE;gBAClB,KAAK,EAAE,KAAK,IAAI,EAAE;gBAClB,cAAc;gBACd,qBAAqB;gBACrB,WAAW,EAAE,YAAY,CAAC,cAAc;aACzC,CAAC,CAAC,QAAQ,EAAE,EAAE,CAAC;YAEhB,GAAG,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAC7B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACrD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,uBAAuB;aAC3C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,2BAA2B,CAAC,GAAY,EAAE,GAAa;QAC5D,IAAI,CAAC;YACH,MAAM,EACJ,SAAS,EACT,YAAY,EACZ,KAAK,EACL,cAAc,EACd,qBAAqB,EACrB,aAAa,EACd,GAAG,GAAG,CAAC,IAAI,CAAC;YAEb,IAAI,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;YACnC,MAAM,IAAI,GAAG,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC;YAE/B,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,iBAAiB,EAAE,wBAAwB;iBAC5C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,gBAAgB;YAChB,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE,CAAC;gBAC/B,MAAM,GAAG,CAAC,MAAM,CAAC,CAAC;YACpB,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,GAAG,EAAE,MAAM,CAAC,CAAC;YAE7D,SAAS;YACT,MAAM,WAAW,GAAG,0BAAW,CAAC,cAAc,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;YAE7D,OAAO,CAAC,GAAG,CAAC,2BAA2B,IAAI,GAAG,EAAE,WAAW,CAAC,CAAC;YAE7D,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAC7B,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,iBAAiB,EAAE,2BAA2B;iBAC/C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,QAAQ;YACR,MAAM,QAAQ,GAAG,oBAAQ,CAAC,yBAAyB,CAAC;gBAClD,SAAS;gBACT,cAAc;gBACd,YAAY;gBACZ,IAAI;gBACJ,MAAM,EAAE,WAAW;aACpB,CAAC,CAAC;YAEH,YAAY;YACZ,MAAM,WAAW,GAAG,IAAI,GAAG,CAAC,YAAY,CAAC,CAAC;YAC1C,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YAC/C,IAAI,KAAK,EAAE,CAAC;gBACV,WAAW,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YAC/C,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,iBAAiB,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;YAE/F,GAAG,CAAC,QAAQ,CAAC,WAAW,CAAC,QAAQ,EAAE,CAAC,CAAC;QACvC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YAC1D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,uBAAuB;aAC3C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACH,MAAM,CAAC,kBAAkB,CAAC,GAAY,EAAE,GAAa;QACnD,IAAI,CAAC;YACH,MAAM,EACJ,UAAU,EACV,IAAI,EACJ,YAAY,EACZ,SAAS,EACT,aAAa,EACd,GAAiB,GAAG,CAAC,IAAI,CAAC;YAE3B,SAAS;YACT,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,SAAS,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC1E,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,iBAAiB;oBACxB,iBAAiB,EAAE,6BAA6B;iBACjD,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,SAAS;YACT,IAAI,UAAU,KAAK,oBAAoB,EAAE,CAAC;gBACxC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,wBAAwB;oBAC/B,iBAAiB,EAAE,iDAAiD;iBACrE,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,QAAQ;YACR,IAAI,YAAY,CAAC;YACjB,IAAI,CAAC;gBACH,YAAY,GAAG,oBAAQ,CAAC,uBAAuB,CAAC,IAAI,CAAC,CAAC;YACxD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,iBAAiB,EAAE,uCAAuC;iBAC3D,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,WAAW;YACX,IAAI,YAAY,CAAC,SAAS,KAAK,SAAS,EAAE,CAAC;gBACzC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,iBAAiB,EAAE,oBAAoB;iBACxC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,YAAY;YACZ,IAAI,YAAY,CAAC,YAAY,KAAK,YAAY,EAAE,CAAC;gBAC/C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,iBAAiB,EAAE,uBAAuB;iBAC3C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,UAAU;YACV,IAAI,CAAC,oBAAQ,CAAC,UAAU,CAAC,aAAa,EAAE,YAAY,CAAC,cAAc,CAAC,EAAE,CAAC;gBACrE,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,KAAK,EAAE,eAAe;oBACtB,iBAAiB,EAAE,0BAA0B;iBAC9C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,SAAS;YACT,MAAM,WAAW,GAAG,oBAAQ,CAAC,mBAAmB,CAAC;gBAC/C,GAAG,EAAE,YAAY,CAAC,IAAI;gBACtB,GAAG,EAAE,SAAS;gBACd,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;aACrC,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,kCAAkC,YAAY,CAAC,IAAI,EAAE,CAAC,CAAC;YAEnE,GAAG,CAAC,IAAI,CAAC;gBACP,YAAY,EAAE,WAAW;gBACzB,UAAU,EAAE,QAAQ;gBACpB,UAAU,EAAE,eAAM,CAAC,uBAAuB;gBAC1C,KAAK,EAAE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC;aACrC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;YAC7C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,uBAAuB;aAC3C,CAAC,CAAC;QACL,CAAC;IACH,CAAC;CACF;AAvWD,oCAuWC"}