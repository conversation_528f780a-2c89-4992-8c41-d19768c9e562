"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MCPStreamableHTTPServer = void 0;
const express_1 = __importDefault(require("express"));
const cors_1 = __importDefault(require("cors"));
const helmet_1 = __importDefault(require("helmet"));
const mcp_js_1 = require("@modelcontextprotocol/sdk/server/mcp.js");
const streamableHttp_js_1 = require("@modelcontextprotocol/sdk/server/streamableHttp.js");
const zod_1 = require("zod");
const config_1 = require("./config");
const jwt_utils_1 = require("./jwt-utils");
const oauth_handler_1 = require("./oauth-handler");
const user_manager_1 = require("./user-manager");
class MCPStreamableHTTPServer {
    constructor() {
        this.transports = new Map();
        this.app = (0, express_1.default)();
        this.setupMiddleware();
        this.setupRoutes();
    }
    createMCPServer(user) {
        const server = new mcp_js_1.McpServer({
            name: 'MCP OAuth Server',
            version: '1.0.0'
        });
        // Echo 工具
        server.registerTool('echo', {
            title: 'Echo Tool',
            description: 'Echo back the input message',
            inputSchema: {
                message: zod_1.z.string().describe('Message to echo back')
            }
        }, async ({ message }) => {
            return {
                content: [
                    {
                        type: 'text',
                        text: `Echo: ${message}`
                    }
                ]
            };
        });
        // 用户信息工具
        server.registerTool('get_user_info', {
            title: 'Get User Info',
            description: 'Get current authenticated user information',
            inputSchema: {}
        }, async () => {
            if (!this.hasScope(user, 'read')) {
                throw new Error('Insufficient permissions: read scope required');
            }
            return {
                content: [
                    {
                        type: 'text',
                        text: JSON.stringify({
                            user: user.sub,
                            scopes: user.scope.split(' '),
                            client: user.aud,
                            expires: new Date(user.exp * 1000).toISOString()
                        }, null, 2)
                    }
                ]
            };
        });
        // 计算器工具
        server.registerTool('calculator', {
            title: 'Calculator',
            description: 'Perform basic arithmetic operations',
            inputSchema: {
                operation: zod_1.z.enum(['add', 'subtract', 'multiply', 'divide']).describe('The arithmetic operation to perform'),
                a: zod_1.z.number().describe('First number'),
                b: zod_1.z.number().describe('Second number')
            }
        }, async ({ operation, a, b }) => {
            if (!this.hasScope(user, 'execute')) {
                throw new Error('Insufficient permissions: execute scope required');
            }
            let result;
            switch (operation) {
                case 'add':
                    result = a + b;
                    break;
                case 'subtract':
                    result = a - b;
                    break;
                case 'multiply':
                    result = a * b;
                    break;
                case 'divide':
                    if (b === 0) {
                        throw new Error('Division by zero is not allowed');
                    }
                    result = a / b;
                    break;
                default:
                    throw new Error(`Unknown operation: ${operation}`);
            }
            return {
                content: [
                    {
                        type: 'text',
                        text: `${a} ${operation} ${b} = ${result}`
                    }
                ]
            };
        });
        // 系统信息工具
        server.registerTool('system_info', {
            title: 'System Info',
            description: 'Get system and server information (admin only)',
            inputSchema: {}
        }, async () => {
            if (!this.hasScope(user, 'admin')) {
                throw new Error('Insufficient permissions: admin scope required');
            }
            return {
                content: [
                    {
                        type: 'text',
                        text: JSON.stringify({
                            server: {
                                name: 'MCP OAuth Server',
                                version: '1.0.0',
                                auth_server_url: config_1.Config.AUTH_SERVER_URL,
                                mcp_server_url: config_1.Config.MCP_SERVER_URL
                            },
                            users: {
                                total: user_manager_1.UserManager.getAllUsers().length,
                                authorized_users: user_manager_1.UserManager.getAllUsers()
                            },
                            scopes: {
                                supported: config_1.Config.SUPPORTED_SCOPES
                            },
                            environment: {
                                node_env: config_1.Config.NODE_ENV,
                                is_development: config_1.Config.IS_DEVELOPMENT
                            }
                        }, null, 2)
                    }
                ]
            };
        });
        // 资源
        server.registerResource('greeting', new mcp_js_1.ResourceTemplate('greeting://{name}', { list: undefined }), {
            title: 'Greeting Resource',
            description: 'A simple greeting resource'
        }, async (uri, { name }) => {
            return {
                contents: [{
                        uri: uri.href,
                        mimeType: 'text/plain',
                        text: `Hello, ${name}! You are authenticated as ${user.sub}.`
                    }]
            };
        });
        // 提示
        server.registerPrompt('user_assistant', {
            title: 'User Assistant',
            description: 'A helpful assistant for the authenticated user',
            argsSchema: {}
        }, () => {
            return {
                messages: [
                    {
                        role: 'user',
                        content: {
                            type: 'text',
                            text: `You are a helpful assistant for ${user.sub}. The user has permissions: ${user.scope}.`
                        }
                    }
                ]
            };
        });
        return server;
    }
    hasScope(user, scope) {
        const userScopes = user.scope.split(' ');
        return userScopes.includes(scope);
    }
    setupMiddleware() {
        // 安全中间件
        if (config_1.Config.IS_DEVELOPMENT) {
            this.app.use((0, helmet_1.default)({
                contentSecurityPolicy: false
            }));
        }
        else {
            this.app.use((0, helmet_1.default)());
        }
        // CORS 配置
        this.app.use((0, cors_1.default)({
            origin: true,
            credentials: true,
            methods: ['GET', 'POST', 'OPTIONS', 'DELETE'],
            allowedHeaders: ['Content-Type', 'Authorization', 'Mcp-Session-Id', 'Last-Event-ID', 'Accept'],
            exposedHeaders: ['Mcp-Session-Id']
        }));
        // 解析 JSON
        this.app.use(express_1.default.json());
    }
    setupRoutes() {
        // 资源服务器元数据端点
        this.app.get('/.well-known/oauth-protected-resource', (req, res) => {
            res.json(oauth_handler_1.OAuthHandler.getResourceServerMetadata());
        });
        // MCP Streamable HTTP 端点
        this.app.all('/mcp', async (req, res) => {
            await this.handleMCPRequest(req, res);
        });
        // 健康检查端点
        this.app.get('/health', (req, res) => {
            res.json({
                status: 'healthy',
                timestamp: new Date().toISOString(),
                service: 'mcp-streamable-http-server'
            });
        });
        // 错误处理中间件
        this.app.use((err, req, res, next) => {
            console.error('MCP streamable HTTP server error:', err);
            res.status(500).json({
                error: 'server_error',
                error_description: 'Internal server error'
            });
        });
        // 404 处理
        this.app.use((req, res) => {
            res.status(404).json({
                error: 'not_found',
                error_description: 'Endpoint not found'
            });
        });
    }
    async handleMCPRequest(req, res) {
        try {
            // 检查认证
            const authHeader = req.headers.authorization;
            const token = authHeader && authHeader.split(' ')[1];
            if (!token) {
                // 返回 OAuth 挑战
                res.status(401).set({
                    'WWW-Authenticate': `Bearer realm="mcp", as_uri="${config_1.Config.AUTH_SERVER_URL}"`
                }).json({
                    jsonrpc: '2.0',
                    error: {
                        code: -32001,
                        message: 'Authentication required',
                        data: {
                            auth_server: config_1.Config.AUTH_SERVER_URL,
                            scopes: config_1.Config.SUPPORTED_SCOPES
                        }
                    },
                    id: req.body?.id || null
                });
                return;
            }
            let user;
            try {
                user = jwt_utils_1.JWTUtils.verifyAccessToken(token);
            }
            catch (error) {
                res.status(401).set({
                    'WWW-Authenticate': `Bearer realm="mcp", as_uri="${config_1.Config.AUTH_SERVER_URL}"`
                }).json({
                    jsonrpc: '2.0',
                    error: {
                        code: -32001,
                        message: 'Invalid or expired access token',
                        data: {
                            auth_server: config_1.Config.AUTH_SERVER_URL,
                            scopes: config_1.Config.SUPPORTED_SCOPES
                        }
                    },
                    id: req.body?.id || null
                });
                return;
            }
            const sessionId = req.headers['mcp-session-id'];
            console.log(`📨 MCP Request from ${user.sub}: ${req.body?.method || req.method}`);
            let transport;
            if (sessionId && this.transports.has(sessionId)) {
                // 重用现有传输
                transport = this.transports.get(sessionId);
            }
            else {
                // 创建新的传输和服务器
                transport = new streamableHttp_js_1.StreamableHTTPServerTransport({
                    sessionIdGenerator: () => this.generateSessionId(),
                    onsessioninitialized: (sessionId) => {
                        this.transports.set(sessionId, transport);
                    }
                });
                // 清理传输
                transport.onclose = () => {
                    if (transport.sessionId) {
                        this.transports.delete(transport.sessionId);
                    }
                };
                // 创建 MCP 服务器实例
                const mcpServer = this.createMCPServer(user);
                // 连接到传输
                await mcpServer.connect(transport);
            }
            // 处理请求
            await transport.handleRequest(req, res, req.body);
        }
        catch (error) {
            console.error('MCP request handler error:', error);
            if (!res.headersSent) {
                res.status(500).json({
                    jsonrpc: '2.0',
                    id: null,
                    error: {
                        code: -32603,
                        message: 'Internal error',
                        data: String(error)
                    }
                });
            }
        }
    }
    generateSessionId() {
        return Math.random().toString(36).substring(2) + Date.now().toString(36);
    }
    async start() {
        return new Promise((resolve, reject) => {
            try {
                this.server = this.app.listen(config_1.Config.MCP_SERVER_PORT, () => {
                    console.log(`🔧 MCP Streamable HTTP Server listening on port ${config_1.Config.MCP_SERVER_PORT}`);
                    resolve();
                });
                this.server.on('error', (error) => {
                    if (error.code === 'EADDRINUSE') {
                        reject(new Error(`Port ${config_1.Config.MCP_SERVER_PORT} is already in use`));
                    }
                    else {
                        reject(error);
                    }
                });
            }
            catch (error) {
                reject(error);
            }
        });
    }
    async stop() {
        return new Promise((resolve) => {
            if (this.server) {
                this.server.close(() => {
                    console.log('🔧 MCP Streamable HTTP Server stopped');
                    resolve();
                });
            }
            else {
                resolve();
            }
        });
    }
}
exports.MCPStreamableHTTPServer = MCPStreamableHTTPServer;
//# sourceMappingURL=mcp-streamable-http-server.js.map