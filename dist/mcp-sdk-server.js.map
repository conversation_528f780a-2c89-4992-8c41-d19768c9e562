{"version": 3, "file": "mcp-sdk-server.js", "sourceRoot": "", "sources": ["../src/mcp-sdk-server.ts"], "names": [], "mappings": ";;;AAAA,oEAAsF;AACtF,wEAAiF;AACjF,6BAAwB;AACxB,qCAAkC;AAClC,iDAA6C;AAC7C,2CAAuC;AAGvC,MAAa,YAAY;IAIvB;QAFQ,gBAAW,GAAuB,IAAI,CAAC;QAG7C,IAAI,CAAC,MAAM,GAAG,IAAI,kBAAS,CAAC;YAC1B,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,KAAa;QAC1B,IAAI,CAAC;YACH,IAAI,CAAC,WAAW,GAAG,oBAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,KAAK,CAAC,yBAAyB,KAAK,EAAE,CAAC,CAAC;QACpD,CAAC;IACH,CAAC;IAED;;OAEG;IACK,QAAQ,CAAC,KAAa;QAC5B,IAAI,CAAC,IAAI,CAAC,WAAW;YAAE,OAAO,KAAK,CAAC;QACpC,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACrD,OAAO,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,UAAU;QAChB,mBAAmB;QACnB,IAAI,CAAC,MAAM,CAAC,YAAY,CACtB,MAAM,EACN;YACE,KAAK,EAAE,WAAW;YAClB,WAAW,EAAE,6BAA6B;YAC1C,WAAW,EAAE;gBACX,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC;aACrD;SACF,EACD,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE;YACpB,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,SAAS,OAAO,EAAE;qBACzB;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,sBAAsB;QACtB,IAAI,CAAC,MAAM,CAAC,YAAY,CACtB,eAAe,EACf;YACE,KAAK,EAAE,eAAe;YACtB,WAAW,EAAE,4CAA4C;YACzD,WAAW,EAAE,EAAE;SAChB,EACD,KAAK,IAAI,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACnE,CAAC;YAED,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACtB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAED,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG;4BAC1B,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC;4BACzC,MAAM,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG;4BAC5B,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;yBAC7D,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,YAAY,CACtB,YAAY,EACZ;YACE,KAAK,EAAE,YAAY;YACnB,WAAW,EAAE,qCAAqC;YAClD,WAAW,EAAE;gBACX,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,qCAAqC,CAAC;gBAC5G,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;gBACtC,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;aACxC;SACF,EACD,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;YAC5B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC9B,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACtE,CAAC;YAED,IAAI,MAAc,CAAC;YACnB,QAAQ,SAAS,EAAE,CAAC;gBAClB,KAAK,KAAK;oBACR,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR,KAAK,UAAU;oBACb,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR,KAAK,UAAU;oBACb,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;wBACZ,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;oBACrD,CAAC;oBACD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;oBACf,MAAM;gBACR;oBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;YACvD,CAAC;YAED,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,GAAG,CAAC,IAAI,SAAS,IAAI,CAAC,MAAM,MAAM,EAAE;qBAC3C;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,uBAAuB;QACvB,IAAI,CAAC,MAAM,CAAC,YAAY,CACtB,aAAa,EACb;YACE,KAAK,EAAE,aAAa;YACpB,WAAW,EAAE,gDAAgD;YAC7D,WAAW,EAAE,EAAE;SAChB,EACD,KAAK,IAAI,EAAE;YACT,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;YAED,OAAO;gBACL,OAAO,EAAE;oBACP;wBACE,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,MAAM,EAAE;gCACN,IAAI,EAAE,kBAAkB;gCACxB,OAAO,EAAE,OAAO;gCAChB,eAAe,EAAE,eAAM,CAAC,eAAe;gCACvC,cAAc,EAAE,eAAM,CAAC,cAAc;6BACtC;4BACD,KAAK,EAAE;gCACL,KAAK,EAAE,0BAAW,CAAC,WAAW,EAAE,CAAC,MAAM;gCACvC,gBAAgB,EAAE,0BAAW,CAAC,WAAW,EAAE;6BAC5C;4BACD,MAAM,EAAE;gCACN,SAAS,EAAE,eAAM,CAAC,gBAAgB;6BACnC;4BACD,WAAW,EAAE;gCACX,QAAQ,EAAE,eAAM,CAAC,QAAQ;gCACzB,cAAc,EAAE,eAAM,CAAC,cAAc;6BACtC;yBACF,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,cAAc;QACpB,WAAW;QACX,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAC1B,cAAc,EACd,IAAI,yBAAgB,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC9D;YACE,KAAK,EAAE,cAAc;YACrB,WAAW,EAAE,8BAA8B;SAC5C,EACD,KAAK,EAAE,GAAG,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;gBAC3B,MAAM,IAAI,KAAK,CAAC,+CAA+C,CAAC,CAAC;YACnE,CAAC;YAED,MAAM,IAAI,GAAG,0BAAW,CAAC,OAAO,CAAC,QAAkB,CAAC,CAAC;YACrD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,MAAM,IAAI,KAAK,CAAC,mBAAmB,QAAQ,EAAE,CAAC,CAAC;YACjD,CAAC;YAED,OAAO;gBACL,QAAQ,EAAE,CAAC;wBACT,GAAG,EAAE,GAAG,CAAC,IAAI;wBACb,QAAQ,EAAE,kBAAkB;wBAC5B,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,QAAQ,EAAE,IAAI,CAAC,QAAQ;4BACvB,MAAM,EAAE,IAAI,CAAC,MAAM;4BACnB,UAAU,EAAE,IAAI;yBACjB,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,SAAS;QACT,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAC1B,UAAU,EACV,IAAI,yBAAgB,CAAC,mBAAmB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC9D;YACE,KAAK,EAAE,mBAAmB;YAC1B,WAAW,EAAE,4BAA4B;SAC1C,EACD,KAAK,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,EAAE,EAAE;YACtB,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW;gBAC/B,CAAC,CAAC,UAAU,IAAI,8BAA8B,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG;gBACrE,CAAC,CAAC,UAAU,IAAI,8BAA8B,CAAC;YAEjD,OAAO;gBACL,QAAQ,EAAE,CAAC;wBACT,GAAG,EAAE,GAAG,CAAC,IAAI;wBACb,QAAQ,EAAE,YAAY;wBACtB,IAAI,EAAE,QAAQ;qBACf,CAAC;aACH,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,wBAAwB;QACxB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAC1B,eAAe,EACf,IAAI,yBAAgB,CAAC,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,CAAC,EAC5D;YACE,KAAK,EAAE,eAAe;YACtB,WAAW,EAAE,4CAA4C;SAC1D,EACD,KAAK,EAAE,GAAG,EAAE,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC5B,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;YACpE,CAAC;YAED,OAAO;gBACL,QAAQ,EAAE,CAAC;wBACT,GAAG,EAAE,GAAG,CAAC,IAAI;wBACb,QAAQ,EAAE,kBAAkB;wBAC5B,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,MAAM,EAAE,SAAS;4BACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;4BACnC,MAAM,EAAE,OAAO,CAAC,MAAM,EAAE;4BACxB,MAAM,EAAE,OAAO,CAAC,WAAW,EAAE;4BAC7B,YAAY,EAAE,IAAI,CAAC,WAAW,EAAE,GAAG,IAAI,IAAI;yBAC5C,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACK,YAAY;QAClB,SAAS;QACT,IAAI,CAAC,MAAM,CAAC,cAAc,CACxB,gBAAgB,EAChB;YACE,KAAK,EAAE,gBAAgB;YACvB,WAAW,EAAE,gDAAgD;SAC9D,EACD,KAAK,IAAI,EAAE;YACT,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,IAAI,OAAO,CAAC;YAClD,MAAM,UAAU,GAAG,IAAI,CAAC,WAAW,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YAE5D,OAAO;gBACL,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE;4BACP,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,mCAAmC,QAAQ,6CAA6C,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,kGAAkG;yBACtN;qBACF;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;QAEF,SAAS;QACT,IAAI,CAAC,MAAM,CAAC,cAAc,CACxB,kBAAkB,EAClB;YACE,KAAK,EAAE,kBAAkB;YACzB,WAAW,EAAE,6CAA6C;SAC3D,EACD,KAAK,IAAI,EAAE;YACT,MAAM,IAAI,GAAG,IAAI,CAAC,WAAW;gBAC3B,CAAC,CAAC,iBAAiB,IAAI,CAAC,WAAW,CAAC,GAAG,kBAAkB,IAAI,CAAC,WAAW,CAAC,KAAK,aAAa,IAAI,CAAC,WAAW,CAAC,GAAG,oBAAoB,IAAI,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE,EAAE;gBACzL,CAAC,CAAC,qFAAqF,CAAC;YAE1F,OAAO;gBACL,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE;4BACP,IAAI,EAAE,MAAM;4BACZ,IAAI;yBACL;qBACF;iBACF;aACF,CAAC;QACJ,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,MAAM,SAAS,GAAG,IAAI,+BAAoB,EAAE,CAAC;QAC7C,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IACvC,CAAC;IAED;;OAEG;IACH,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;CACF;AAzVD,oCAyVC"}