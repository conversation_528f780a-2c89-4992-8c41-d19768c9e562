{"version": 3, "file": "mcp-official-server.js", "sourceRoot": "", "sources": ["../src/mcp-official-server.ts"], "names": [], "mappings": ";;;;;;AAAA,sDAA8D;AAC9D,gDAAwB;AACxB,6CAAyC;AACzC,oEAAoE;AACpE,0FAAmG;AACnG,iEAAyE;AACzE,6BAAwB;AACxB,qCAAkC;AAClC,2CAAuC;AACvC,mDAA+C;AAG/C,MAAa,iBAAiB;IAK5B;QAFQ,eAAU,GAA2D,EAAE,CAAC;QAG9E,IAAI,CAAC,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;QACrB,IAAI,CAAC,eAAe,EAAE,CAAC;QACvB,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAEO,eAAe;QACrB,gCAAgC;QAChC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAA,cAAI,EAAC;YAChB,MAAM,EAAE,IAAI;YACZ,WAAW,EAAE,IAAI;YACjB,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,CAAC;YAC7C,cAAc,EAAE,CAAC,cAAc,EAAE,eAAe,EAAE,gBAAgB,CAAC;YACnE,cAAc,EAAE,CAAC,gBAAgB,CAAC;SACnC,CAAC,CAAC,CAAC;QAEJ,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAC/B,CAAC;IAEO,WAAW;QACjB,aAAa;QACb,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uCAAuC,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACpF,GAAG,CAAC,IAAI,CAAC,4BAAY,CAAC,yBAAyB,EAAE,CAAC,CAAC;QACrD,CAAC,CAAC,CAAC;QAEH,yBAAyB;QACzB,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;YACzD,MAAM,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QACxC,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YACtD,GAAG,CAAC,IAAI,CAAC;gBACP,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE,qBAAqB;aAC/B,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,UAAU;QACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAQ,EAAE,GAAY,EAAE,GAAa,EAAE,IAAS,EAAE,EAAE;YAChE,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC;YACjD,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,cAAc;gBACrB,iBAAiB,EAAE,uBAAuB;aAC3C,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,KAAK,EAAE,WAAW;gBAClB,iBAAiB,EAAE,oBAAoB;aACxC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,gBAAgB,CAAC,GAAY,EAAE,GAAa;QACxD,IAAI,CAAC;YACH,SAAS;YACT,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;YAC7C,MAAM,KAAK,GAAG,UAAU,IAAI,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAErD,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;oBAClB,kBAAkB,EAAE,+BAA+B,eAAM,CAAC,eAAe,0CAA0C;iBACpH,CAAC,CAAC,IAAI,CAAC;oBACN,KAAK,EAAE,eAAe;oBACtB,iBAAiB,EAAE,sBAAsB;iBAC1C,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,IAAI,IAAiB,CAAC;YACtB,IAAI,CAAC;gBACH,IAAI,GAAG,oBAAQ,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC3C,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;oBAClB,kBAAkB,EAAE,+BAA+B,eAAM,CAAC,eAAe,0CAA0C;iBACpH,CAAC,CAAC,IAAI,CAAC;oBACN,KAAK,EAAE,eAAe;oBACtB,iBAAiB,EAAE,iCAAiC;iBACrD,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,uBAAuB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;YAE/C,YAAY;YACZ,MAAM,SAAS,GAAG,GAAG,CAAC,OAAO,CAAC,gBAAgB,CAAuB,CAAC;YACtE,IAAI,SAAwC,CAAC;YAE7C,IAAI,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;gBAC5C,SAAS;gBACT,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;YACzC,CAAC;iBAAM,IAAI,CAAC,SAAS,IAAI,IAAA,8BAAmB,EAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;gBACvD,UAAU;gBACV,SAAS,GAAG,IAAI,iDAA6B,CAAC;oBAC5C,kBAAkB,EAAE,GAAG,EAAE,CAAC,IAAA,wBAAU,GAAE;oBACtC,oBAAoB,EAAE,CAAC,SAAS,EAAE,EAAE;wBAClC,OAAO;wBACP,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,GAAG,SAAS,CAAC;wBACvC,OAAO,CAAC,GAAG,CAAC,uBAAuB,SAAS,EAAE,CAAC,CAAC;oBAClD,CAAC;oBACD,4BAA4B,EAAE,IAAI;oBAClC,YAAY,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC;iBACzC,CAAC,CAAC;gBAEH,OAAO;gBACP,SAAS,CAAC,OAAO,GAAG,GAAG,EAAE;oBACvB,IAAI,SAAS,CAAC,SAAS,EAAE,CAAC;wBACxB,OAAO,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;wBAC5C,OAAO,CAAC,GAAG,CAAC,2BAA2B,SAAS,CAAC,SAAS,EAAE,CAAC,CAAC;oBAChE,CAAC;gBACH,CAAC,CAAC;gBAEF,eAAe;gBACf,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;gBAE7C,cAAc;gBACd,MAAM,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACrC,CAAC;iBAAM,CAAC;gBACN,OAAO;gBACP,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBACnB,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,IAAI,EAAE,CAAC,KAAK;wBACZ,OAAO,EAAE,2CAA2C;qBACrD;oBACD,EAAE,EAAE,IAAI;iBACT,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,OAAO;YACP,MAAM,SAAS,CAAC,aAAa,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC;QACpD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,CAAC,KAAK,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;YAC3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,IAAI,EAAE,CAAC,KAAK;oBACZ,OAAO,EAAE,gBAAgB;oBACzB,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC;iBACpB;gBACD,EAAE,EAAE,IAAI;aACT,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAEO,eAAe,CAAC,IAAiB;QACvC,MAAM,MAAM,GAAG,IAAI,kBAAS,CAAC;YAC3B,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE,OAAO;SACjB,CAAC,CAAC;QAEH,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QAEzC,sBAAsB;QACtB,MAAM,CAAC,YAAY,CACjB,MAAM,EACN;YACE,KAAK,EAAE,WAAW;YAClB,WAAW,EAAE,6BAA6B;YAC1C,WAAW,EAAE;gBACX,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,sBAAsB,CAAC;aACrD;SACF,EACD,KAAK,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC;YACtB,OAAO,EAAE,CAAC;oBACR,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS,OAAO,EAAE;iBACzB,CAAC;SACH,CAAC,CACH,CAAC;QAEF,sBAAsB;QACtB,IAAI,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YAChC,MAAM,CAAC,YAAY,CACjB,eAAe,EACf;gBACE,KAAK,EAAE,eAAe;gBACtB,WAAW,EAAE,4CAA4C;gBACzD,WAAW,EAAE,EAAE;aAChB,EACD,KAAK,IAAI,EAAE,CAAC,CAAC;gBACX,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,IAAI,EAAE,IAAI,CAAC,GAAG;4BACd,MAAM,EAAE,UAAU;4BAClB,MAAM,EAAE,IAAI,CAAC,GAAG;4BAChB,OAAO,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,WAAW,EAAE;yBACjD,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC;QAED,wBAAwB;QACxB,IAAI,UAAU,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YACnC,MAAM,CAAC,YAAY,CACjB,YAAY,EACZ;gBACE,KAAK,EAAE,YAAY;gBACnB,WAAW,EAAE,qCAAqC;gBAClD,WAAW,EAAE;oBACX,SAAS,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,qCAAqC,CAAC;oBAC5G,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,cAAc,CAAC;oBACtC,CAAC,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,eAAe,CAAC;iBACxC;aACF,EACD,KAAK,EAAE,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE;gBAC5B,IAAI,MAAc,CAAC;gBACnB,QAAQ,SAAS,EAAE,CAAC;oBAClB,KAAK,KAAK;wBACR,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;wBACf,MAAM;oBACR,KAAK,UAAU;wBACb,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;wBACf,MAAM;oBACR,KAAK,UAAU;wBACb,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;wBACf,MAAM;oBACR,KAAK,QAAQ;wBACX,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC;4BACZ,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;wBACrD,CAAC;wBACD,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;wBACf,MAAM;oBACR;wBACE,MAAM,IAAI,KAAK,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAC;gBACvD,CAAC;gBAED,OAAO;oBACL,OAAO,EAAE,CAAC;4BACR,IAAI,EAAE,MAAM;4BACZ,IAAI,EAAE,GAAG,CAAC,IAAI,SAAS,IAAI,CAAC,MAAM,MAAM,EAAE;yBAC3C,CAAC;iBACH,CAAC;YACJ,CAAC,CACF,CAAC;QACJ,CAAC;QAED,uBAAuB;QACvB,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;YACjC,MAAM,CAAC,YAAY,CACjB,aAAa,EACb;gBACE,KAAK,EAAE,aAAa;gBACpB,WAAW,EAAE,gDAAgD;gBAC7D,WAAW,EAAE,EAAE;aAChB,EACD,KAAK,IAAI,EAAE,CAAC,CAAC;gBACX,OAAO,EAAE,CAAC;wBACR,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;4BACnB,MAAM,EAAE;gCACN,IAAI,EAAE,kBAAkB;gCACxB,OAAO,EAAE,OAAO;gCAChB,eAAe,EAAE,eAAM,CAAC,eAAe;gCACvC,cAAc,EAAE,eAAM,CAAC,cAAc;6BACtC;4BACD,WAAW,EAAE;gCACX,QAAQ,EAAE,eAAM,CAAC,QAAQ;gCACzB,cAAc,EAAE,eAAM,CAAC,cAAc;6BACtC;4BACD,IAAI,EAAE;gCACJ,gBAAgB,EAAE,IAAI,CAAC,GAAG;gCAC1B,MAAM,EAAE,UAAU;6BACnB;yBACF,EAAE,IAAI,EAAE,CAAC,CAAC;qBACZ,CAAC;aACH,CAAC,CACH,CAAC;QACJ,CAAC;QAED,OAAO;QACP,MAAM,CAAC,gBAAgB,CACrB,UAAU,EACV,kBAAkB,EAClB;YACE,KAAK,EAAE,mBAAmB;YAC1B,WAAW,EAAE,4BAA4B;YACzC,QAAQ,EAAE,YAAY;SACvB,EACD,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC;YACd,QAAQ,EAAE,CAAC;oBACT,GAAG,EAAE,GAAG,CAAC,IAAI;oBACb,QAAQ,EAAE,YAAY;oBACtB,IAAI,EAAE,0CAA0C,IAAI,CAAC,GAAG,iBAAiB,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;iBAClG,CAAC;SACH,CAAC,CACH,CAAC;QAEF,OAAO;QACP,MAAM,CAAC,cAAc,CACnB,gBAAgB,EAChB;YACE,KAAK,EAAE,gBAAgB;YACvB,WAAW,EAAE,gDAAgD;YAC7D,UAAU,EAAE,EAAE;SACf,EACD,GAAG,EAAE,CAAC,CAAC;YACL,QAAQ,EAAE,CAAC;oBACT,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE;wBACP,IAAI,EAAE,MAAM;wBACZ,IAAI,EAAE,mCAAmC,IAAI,CAAC,GAAG,+BAA+B,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;qBACzG;iBACF,CAAC;SACH,CAAC,CACH,CAAC;QAEF,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YACrC,IAAI,CAAC;gBACH,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,eAAM,CAAC,eAAe,EAAE,GAAG,EAAE;oBACzD,OAAO,CAAC,GAAG,CAAC,4CAA4C,eAAM,CAAC,eAAe,EAAE,CAAC,CAAC;oBAClF,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;gBAEH,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,KAAU,EAAE,EAAE;oBACrC,IAAI,KAAK,CAAC,IAAI,KAAK,YAAY,EAAE,CAAC;wBAChC,MAAM,CAAC,IAAI,KAAK,CAAC,QAAQ,eAAM,CAAC,eAAe,oBAAoB,CAAC,CAAC,CAAC;oBACxE,CAAC;yBAAM,CAAC;wBACN,MAAM,CAAC,KAAK,CAAC,CAAC;oBAChB,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,CAAC;YAChB,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,IAAI;QACR,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC7B,SAAS;YACT,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;gBACjD,SAAS,CAAC,KAAK,EAAE,CAAC;YACpB,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,UAAU,GAAG,EAAE,CAAC;YAErB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,EAAE;oBACrB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;oBAC9C,OAAO,EAAE,CAAC;gBACZ,CAAC,CAAC,CAAC;YACL,CAAC;iBAAM,CAAC;gBACN,OAAO,EAAE,CAAC;YACZ,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AA1WD,8CA0WC"}