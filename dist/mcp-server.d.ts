export declare class MCPServer {
    private app;
    private server;
    private mcpSDKServer;
    constructor();
    private setupMiddleware;
    private authenticateToken;
    private setupRoutes;
    private handleMCPRequest;
    private handleSDKRequest;
    private handleInitialize;
    private handleToolsList;
    private handleToolsCallSDK;
    private handleToolsCall;
    private handleResourcesList;
    private handleResourcesRead;
    private handleResourcesReadSDK;
    private handlePromptsList;
    private handlePromptsGet;
    private handleSSEConnection;
    private handleToolsRequest;
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=mcp-server.d.ts.map