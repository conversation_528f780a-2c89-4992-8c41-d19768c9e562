export declare class MCPServer {
    private app;
    private server;
    constructor();
    private setupMiddleware;
    private authenticateToken;
    private setupRoutes;
    private handleMCPRequest;
    private handleInitialize;
    private handleToolsList;
    private handleToolsCall;
    private handleResourcesList;
    private handleResourcesRead;
    private handleSSEConnection;
    private handleToolsRequest;
    start(): Promise<void>;
    stop(): Promise<void>;
}
//# sourceMappingURL=mcp-server.d.ts.map