const crypto = require('crypto');
const jwt = require('jsonwebtoken');

// 生成 PKCE 参数
const codeVerifier = crypto.randomBytes(32).toString('base64url');
const codeChallenge = crypto.createHash('sha256').update(codeVerifier).digest('base64url');

// 客户端 ID
const clientId = jwt.sign({
  redirect_uris: ['http://localhost:8080/callback'],
  client_name: 'MCP Inspector',
  issued_at: Math.floor(Date.now() / 1000),
  iat: Math.floor(Date.now() / 1000)
}, 'your-secret-key');

const state = 'test_' + Math.floor(Date.now() / 1000);

const authUrl = 'http://localhost:3001/oauth/authorize?' + new URLSearchParams({
  response_type: 'code',
  client_id: clientId,
  redirect_uri: 'http://localhost:8080/callback',
  scope: 'read write execute',
  state: state,
  code_challenge: codeChallenge,
  code_challenge_method: 'S256'
}).toString();

console.log('Authorization URL:');
console.log(authUrl);
console.log('');
console.log('Code Verifier (save this):', codeVerifier);
console.log('State:', state);
