#!/usr/bin/env node

/**
 * MCP HTTP Bridge for Cursor
 * 
 * This script bridges between Cursor's stdio MCP transport and our HTTP MCP server.
 * It reads JSON-RPC messages from stdin and forwards them to the HTTP server.
 */

const http = require('http');
const readline = require('readline');

// 配置
const MCP_SERVER_URL = process.env.MCP_SERVER_URL || 'http://localhost:3002/mcp';
const ACCESS_TOKEN = process.env.ACCESS_TOKEN || '';

if (!ACCESS_TOKEN) {
  console.error('Error: ACCESS_TOKEN environment variable is required');
  process.exit(1);
}

// 创建 readline 接口来读取 stdin
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
  terminal: false
});

// 会话管理
let sessionId = null;

/**
 * 发送 HTTP 请求到 MCP 服务器
 */
function sendHttpRequest(jsonRpcMessage) {
  return new Promise((resolve, reject) => {
    const postData = JSON.stringify(jsonRpcMessage);
    
    const options = {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Content-Length': Buffer.byteLength(postData),
        'Authorization': `Bearer ${ACCESS_TOKEN}`
      }
    };

    // 添加会话 ID（如果有）
    if (sessionId) {
      options.headers['Mcp-Session-Id'] = sessionId;
    }

    const req = http.request(MCP_SERVER_URL, options, (res) => {
      let data = '';

      // 保存会话 ID
      if (res.headers['mcp-session-id']) {
        sessionId = res.headers['mcp-session-id'];
      }

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve(response);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.write(postData);
    req.end();
  });
}

/**
 * 处理来自 Cursor 的 JSON-RPC 消息
 */
async function handleMessage(line) {
  try {
    const message = JSON.parse(line);
    
    // 转发到 HTTP 服务器
    const response = await sendHttpRequest(message);
    
    // 发送响应回 Cursor
    console.log(JSON.stringify(response));
    
  } catch (error) {
    // 发送错误响应
    const errorResponse = {
      jsonrpc: '2.0',
      id: null,
      error: {
        code: -32603,
        message: 'Internal error',
        data: error.message
      }
    };
    
    console.log(JSON.stringify(errorResponse));
  }
}

// 监听来自 Cursor 的消息
rl.on('line', handleMessage);

// 处理进程退出
process.on('SIGINT', () => {
  rl.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  rl.close();
  process.exit(0);
});

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled rejection at:', promise, 'reason:', reason);
  process.exit(1);
});
