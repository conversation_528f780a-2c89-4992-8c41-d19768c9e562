{"name": "mcp-oauth-server", "version": "1.0.0", "description": "MCP OAuth 2.1 Server with Authorization Server and Resource Server", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "dev:mcp": "ts-node src/mcp-stdio.ts", "start": "node dist/index.js", "start:mcp": "node dist/mcp-stdio.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "keywords": ["mcp", "o<PERSON>h", "authorization-server", "resource-server", "typescript"], "author": "", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.15.1", "@types/express": "^4.17.0", "@types/jsonwebtoken": "^9.0.0", "@types/node": "^20.0.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "express": "^4.18.0", "express-session": "^1.17.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "zod": "^3.25.76"}, "devDependencies": {"@types/cors": "^2.8.0", "@types/express-session": "^1.17.0", "@types/jest": "^29.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "nodemon": "^3.0.0", "ts-jest": "^29.0.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}}