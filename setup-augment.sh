#!/bin/bash

# 设置 Augment MCP 配置的自动化脚本

set -e

echo "🔧 MCP OAuth 服务器 - Augment 配置助手"
echo "=================================="

# 检查服务器是否运行
echo "1. 检查 MCP OAuth 服务器状态..."
if curl -s http://localhost:3001/health > /dev/null 2>&1; then
    echo "✅ 认证服务器运行正常"
else
    echo "❌ 认证服务器未运行，请先启动服务器："
    echo "   npm run dev"
    exit 1
fi

if curl -s http://localhost:3002/health > /dev/null 2>&1; then
    echo "✅ MCP 服务器运行正常"
else
    echo "❌ MCP 服务器未运行，请先启动服务器："
    echo "   npm run dev"
    exit 1
fi

# 检查是否有有效的访问令牌
echo ""
echo "2. 检查访问令牌..."
if [ -f ".env.test" ]; then
    ACCESS_TOKEN=$(grep "ACCESS_TOKEN=" .env.test | cut -d'=' -f2)
    if [ -n "$ACCESS_TOKEN" ]; then
        echo "✅ 找到访问令牌"
        
        # 测试令牌是否有效
        if curl -s -H "Authorization: Bearer $ACCESS_TOKEN" \
           -H "Content-Type: application/json" \
           -d '{"jsonrpc":"2.0","id":1,"method":"initialize","params":{"protocolVersion":"2024-11-05","capabilities":{},"clientInfo":{"name":"test","version":"1.0.0"}}}' \
           http://localhost:3002/mcp | grep -q '"result"'; then
            echo "✅ 访问令牌有效"
        else
            echo "❌ 访问令牌无效或已过期"
            echo "请运行以下命令获取新令牌："
            echo "   ./simple-test.sh"
            echo "   然后在浏览器中完成授权，并运行："
            echo "   ./exchange-token.sh <授权码>"
            exit 1
        fi
    else
        echo "❌ 未找到访问令牌"
        exit 1
    fi
else
    echo "❌ 未找到 .env.test 文件"
    echo "请先运行 OAuth 流程获取访问令牌"
    exit 1
fi

# 生成 Augment 配置
echo ""
echo "3. 生成 Augment MCP 配置..."

cat > augment-mcp-config.json << EOF
{
  "mcpServers": {
    "oauth-demo-server": {
      "transport": {
        "type": "http",
        "url": "http://localhost:3002/mcp",
        "headers": {
          "Authorization": "Bearer $ACCESS_TOKEN"
        }
      },
      "description": "MCP OAuth Demo Server with authentication",
      "capabilities": {
        "tools": true,
        "resources": true,
        "prompts": true
      }
    }
  }
}
EOF

echo "✅ 配置文件已生成: augment-mcp-config.json"

# 显示配置信息
echo ""
echo "4. 配置信息"
echo "==========="
echo "MCP 服务器 URL: http://localhost:3002/mcp"
echo "访问令牌: ${ACCESS_TOKEN:0:20}..."
echo "配置文件: augment-mcp-config.json"

echo ""
echo "5. 下一步操作"
echo "============"
echo "1. 将 augment-mcp-config.json 的内容添加到 Augment 的 MCP 配置中"
echo "2. 或者将整个配置复制到 Augment 的配置文件："
echo ""

# 检测操作系统并显示配置文件路径
if [[ "$OSTYPE" == "darwin"* ]]; then
    CONFIG_PATH="~/Library/Application Support/Augment/mcp.json"
elif [[ "$OSTYPE" == "msys" || "$OSTYPE" == "win32" ]]; then
    CONFIG_PATH="%APPDATA%\\Augment\\mcp.json"
else
    CONFIG_PATH="~/.config/Augment/mcp.json"
fi

echo "   配置文件路径: $CONFIG_PATH"
echo ""
echo "3. 重启 Augment 或重新加载 MCP 配置"

echo ""
echo "🎯 可用功能"
echo "=========="
echo "• echo - 回显消息"
echo "• get_user_info - 获取用户信息"
echo "• calculator - 基础计算器"
echo "• system_info - 系统信息 (需要 admin 权限)"
echo "• greeting://{name} - 问候资源"
echo "• user_assistant - 用户助手提示"

echo ""
echo "✅ 配置完成！"
