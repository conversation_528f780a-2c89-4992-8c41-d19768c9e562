<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>授权确认 - MCP OAuth</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 0;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .authorize-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 500px;
        }
        .header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 1.8rem;
        }
        .header p {
            color: #666;
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }
        .client-info {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin-bottom: 1.5rem;
            border-left: 4px solid #667eea;
        }
        .client-info h3 {
            margin: 0 0 0.5rem 0;
            color: #333;
        }
        .client-info p {
            margin: 0.25rem 0;
            color: #666;
            font-size: 0.9rem;
        }
        .scopes {
            margin-bottom: 1.5rem;
        }
        .scopes h3 {
            color: #333;
            margin-bottom: 1rem;
        }
        .scope-item {
            display: flex;
            align-items: center;
            padding: 0.5rem;
            margin-bottom: 0.5rem;
            background: #f8f9fa;
            border-radius: 5px;
        }
        .scope-item input[type="checkbox"] {
            margin-right: 0.75rem;
        }
        .scope-item label {
            flex: 1;
            color: #333;
            cursor: pointer;
        }
        .scope-description {
            font-size: 0.8rem;
            color: #666;
            margin-left: 1.5rem;
        }
        .actions {
            display: flex;
            gap: 1rem;
        }
        .btn {
            flex: 1;
            padding: 0.75rem;
            border: none;
            border-radius: 5px;
            font-size: 1rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        .btn-primary {
            background: #28a745;
            color: white;
        }
        .btn-primary:hover {
            background: #218838;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            padding: 0.75rem;
            border-radius: 5px;
            margin-bottom: 1rem;
            border: 1px solid #ffeaa7;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="authorize-container">
        <div class="header">
            <h1>授权确认</h1>
            <p>应用程序请求访问您的 MCP 资源</p>
        </div>
        
        <div class="client-info">
            <h3 id="clientName">应用程序</h3>
            <p><strong>客户端 ID:</strong> <span id="clientId"></span></p>
            <p><strong>重定向 URI:</strong> <span id="redirectUri"></span></p>
        </div>
        
        <div class="warning">
            <strong>注意:</strong> 授权后，该应用程序将能够以您的身份访问所选的 MCP 资源和功能。
        </div>
        
        <form id="authorizeForm" method="POST" action="/oauth/authorize">
            <input type="hidden" name="client_id" id="hiddenClientId">
            <input type="hidden" name="redirect_uri" id="hiddenRedirectUri">
            <input type="hidden" name="state" id="hiddenState">
            <input type="hidden" name="code_challenge" id="hiddenCodeChallenge">
            <input type="hidden" name="code_challenge_method" id="hiddenCodeChallengeMethod">
            <input type="hidden" name="response_type" id="hiddenResponseType">

            <div class="scopes">
                <h3>请求的权限</h3>
                <div id="scopesList"></div>
            </div>

            <div class="actions">
                <button type="button" class="btn btn-secondary" onclick="denyAuthorization()">拒绝</button>
                <button type="submit" class="btn btn-primary">授权</button>
            </div>
        </form>
    </div>

    <script>
        // 权限描述映射
        const scopeDescriptions = {
            'read': '读取 MCP 资源和数据',
            'write': '修改和创建 MCP 资源',
            'execute': '执行 MCP 工具和操作',
            'admin': '管理 MCP 服务器配置'
        };
        
        // 从 URL 参数获取授权信息
        const urlParams = new URLSearchParams(window.location.search);
        const clientId = urlParams.get('client_id');
        const redirectUri = urlParams.get('redirect_uri');
        const state = urlParams.get('state');
        const scope = urlParams.get('scope');
        const codeChallenge = urlParams.get('code_challenge');
        const codeChallengeMethod = urlParams.get('code_challenge_method');
        const responseType = urlParams.get('response_type');
        const clientName = urlParams.get('client_name') || '未知应用程序';
        
        // 填充页面信息
        document.getElementById('clientName').textContent = clientName;
        document.getElementById('clientId').textContent = clientId || '';
        document.getElementById('redirectUri').textContent = redirectUri || '';
        
        // 填充隐藏字段
        document.getElementById('hiddenClientId').value = clientId || '';
        document.getElementById('hiddenRedirectUri').value = redirectUri || '';
        document.getElementById('hiddenState').value = state || '';
        document.getElementById('hiddenCodeChallenge').value = codeChallenge || '';
        document.getElementById('hiddenCodeChallengeMethod').value = codeChallengeMethod || '';
        document.getElementById('hiddenResponseType').value = responseType || '';
        
        // 生成权限列表
        const scopes = scope ? scope.split(' ') : [];
        const scopesList = document.getElementById('scopesList');
        
        scopes.forEach(scopeName => {
            const scopeItem = document.createElement('div');
            scopeItem.className = 'scope-item';
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.name = 'scopes';
            checkbox.value = scopeName;
            checkbox.id = `scope_${scopeName}`;
            checkbox.checked = true;
            
            const label = document.createElement('label');
            label.htmlFor = `scope_${scopeName}`;
            label.textContent = scopeName;
            
            const description = document.createElement('div');
            description.className = 'scope-description';
            description.textContent = scopeDescriptions[scopeName] || '未知权限';
            
            scopeItem.appendChild(checkbox);
            scopeItem.appendChild(label);
            scopesList.appendChild(scopeItem);
            scopesList.appendChild(description);
        });
        
        // 拒绝授权
        function denyAuthorization() {
            const redirectUri = document.getElementById('hiddenRedirectUri').value;
            const state = document.getElementById('hiddenState').value;
            
            let denyUrl = redirectUri + '?error=access_denied&error_description=User denied authorization';
            if (state) {
                denyUrl += '&state=' + encodeURIComponent(state);
            }
            
            window.location.href = denyUrl;
        }
    </script>
</body>
</html>
