# MCP OAuth 2.1 服务器

符合 MCP (Model Context Protocol) 授权规范的 OAuth 2.1 服务器系统，包含授权服务器和 MCP 资源服务器。

## 功能特性

### 核心功能
- ✅ **OAuth 2.1 授权流程** - 完整的授权码流程实现
- ✅ **动态客户端注册** - 支持 RFC7591 动态客户端注册
- ✅ **PKCE 安全验证** - 强制使用 S256 方法
- ✅ **用户白名单控制** - 只有预定义用户可以授权
- ✅ **JWT 无状态设计** - 无需数据库存储
- ✅ **MCP 协议支持** - Streamable HTTP 和 SSE 传输

### 安全特性
- 🔒 HTTPS 强制（生产环境）
- 🔒 重定向 URI 验证
- 🔒 状态参数验证（防 CSRF）
- 🔒 短期令牌（1小时过期）
- 🔒 标准 401 响应格式

## 快速开始

### 1. 安装依赖

```bash
npm install
```

### 2. 配置环境变量

复制 `.env` 文件并根据需要修改：

```bash
# 授权用户列表
AUTHORIZED_USERS=zhang_san,li_si,admin

# 用户认证信息
ZHANG_SAN_PASSWORD=dev123
ZHANG_SAN_SCOPES=read,write,execute

LI_SI_PASSWORD=pm456
LI_SI_SCOPES=read

ADMIN_PASSWORD=admin789
ADMIN_SCOPES=read,write,execute,admin

# 服务器配置
AUTH_SERVER_URL=http://localhost:3001
MCP_SERVER_URL=http://localhost:3002
AUTH_SERVER_PORT=3001
MCP_SERVER_PORT=3002

# 密钥配置（生产环境请更改）
JWT_SECRET=your-jwt-secret-here
AUTH_CODE_SECRET=your-auth-code-secret
CLIENT_SECRET=your-client-secret
```

### 3. 启动服务器

```bash
# 开发模式
npm run dev

# 生产模式
npm run build
npm start
```

## API 端点

### 授权服务器 (端口 3001)

| 端点 | 方法 | 描述 |
|------|------|------|
| `/.well-known/oauth-authorization-server` | GET | 授权服务器元数据 |
| `/oauth/register` | POST | 动态客户端注册 |
| `/oauth/authorize` | GET | 授权端点 |
| `/oauth/token` | POST | 令牌端点 |
| `/login` | GET/POST | 用户登录 |

### MCP 服务器 (端口 3002)

| 端点 | 方法 | 描述 |
|------|------|------|
| `/.well-known/oauth-protected-resource` | GET | 资源服务器元数据 |
| `/mcp` | POST | Streamable HTTP 传输 |
| `/sse` | GET | SSE 传输（兼容性） |
| `/mcp/v1/tools` | GET | MCP 工具列表 |

## OAuth 2.1 流程

### 1. 客户端注册

```bash
curl -X POST http://localhost:3001/oauth/register \
  -H "Content-Type: application/json" \
  -d '{
    "client_name": "My MCP Client",
    "redirect_uris": ["http://localhost:8080/callback"]
  }'
```

### 2. 授权请求

```
http://localhost:3001/oauth/authorize?
  response_type=code&
  client_id=CLIENT_ID&
  redirect_uri=http://localhost:8080/callback&
  scope=read write&
  state=random_state&
  code_challenge=CODE_CHALLENGE&
  code_challenge_method=S256
```

### 3. 令牌交换

```bash
curl -X POST http://localhost:3001/oauth/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&code=AUTH_CODE&redirect_uri=http://localhost:8080/callback&client_id=CLIENT_ID&code_verifier=CODE_VERIFIER"
```

### 4. 访问 MCP 资源

```bash
curl -X POST http://localhost:3002/mcp \
  -H "Authorization: Bearer ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/list"
  }'
```

## MCP 工具示例

系统提供以下示例工具：

### echo 工具
```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "echo",
    "arguments": {
      "message": "Hello, MCP!"
    }
  }
}
```

### get_user_info 工具
```json
{
  "jsonrpc": "2.0",
  "id": 2,
  "method": "tools/call",
  "params": {
    "name": "get_user_info",
    "arguments": {}
  }
}
```

## 开发

### 项目结构

```
src/
├── auth-server.ts          # 授权服务器实现
├── mcp-server.ts           # MCP 资源服务器实现
├── oauth-handler.ts        # OAuth 流程处理
├── user-manager.ts         # 用户管理
├── jwt-utils.ts            # JWT 工具函数
├── types.ts                # TypeScript 类型定义
├── config.ts               # 配置管理
└── index.ts                # 应用入口

public/
├── login.html              # 登录页面
├── authorize.html          # 授权确认页面
└── error.html              # 错误页面
```

### 测试

```bash
# 运行测试
npm test

# 运行测试并监听变化
npm run test:watch
```

### 代码检查

```bash
# 运行 ESLint
npm run lint

# 自动修复代码风格问题
npm run lint:fix
```

## 部署

### 生产环境配置

1. 设置环境变量 `NODE_ENV=production`
2. 使用 HTTPS
3. 更改默认密钥
4. 限制 CORS 来源
5. 配置反向代理

### Docker 部署

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3001 3002
CMD ["npm", "start"]
```

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！
