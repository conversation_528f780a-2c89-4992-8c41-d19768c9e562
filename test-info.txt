测试信息
========
客户端 ID: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcyMDA0NiwiaWF0IjoxNzUyNzIwMDQ2fQ.xZqJcVuA34DZ6MupxR50hNVmGNtrZuXZTChTXi_Nn7E
Code Verifier: VBATboCAnB1tjBuggq5jtUXabztn6ApdGnNCMDss
Code Challenge: tgonDCBsDVXEJcucI5KEge8Pa6Qn3-r6Xw3hJNtH0Vw
State: test_1752720046
授权 URL: http://localhost:3001/oauth/authorize?response_type=code&client_id=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcyMDA0NiwiaWF0IjoxNzUyNzIwMDQ2fQ.xZqJcVuA34DZ6MupxR50hNVmGNtrZuXZTChTXi_Nn7E&redirect_uri=http%3A//localhost%3A8080/callback&scope=read%20write%20execute&state=test_1752720046&code_challenge=tgonDCBsDVXEJcucI5KEge8Pa6Qn3-r6Xw3hJNtH0Vw&code_challenge_method=S256

下一步操作:
1. 在浏览器中访问授权 URL
2. 使用测试账号登录
3. 授权后从重定向 URL 中复制授权码
4. 运行: ./exchange-token.sh <授权码>
