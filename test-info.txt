测试信息
========
客户端 ID: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcxNjk0OCwiaWF0IjoxNzUyNzE2OTQ4fQ.YYndOAzhQGFvGrTwakRBZZYyLsFkXOH9WxYVs4oC4mc
Code Verifier: kfTba63L5jRWTmwAc0qiMlm2W9CUjG4AF2GpMoKDw
Code Challenge: Sf-YIZWVQLwO6iStm4vdxKCcOKP0b10rf4uIXlO4wJw
State: test_1752716948
授权 URL: http://localhost:3001/oauth/authorize?response_type=code&client_id=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcxNjk0OCwiaWF0IjoxNzUyNzE2OTQ4fQ.YYndOAzhQGFvGrTwakRBZZYyLsFkXOH9WxYVs4oC4mc&redirect_uri=http%3A//localhost%3A8080/callback&scope=read%20write%20execute&state=test_1752716948&code_challenge=Sf-YIZWVQLwO6iStm4vdxKCcOKP0b10rf4uIXlO4wJw&code_challenge_method=S256

下一步操作:
1. 在浏览器中访问授权 URL
2. 使用测试账号登录
3. 授权后从重定向 URL 中复制授权码
4. 运行: ./exchange-token.sh <授权码>
