测试信息
========
客户端 ID: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcxOTQwMywiaWF0IjoxNzUyNzE5NDAzfQ.D55Y9iOBbi86uUL9Z9z15LCK3Vq-pBoktKM5z6LYx5I
Code Verifier: ZFDW16ibS2ov4JjsF9OJPVcm0vLVIBFxtTQPeS8Ic
Code Challenge: vl6bXuynzJsyMXdk4OeS662orxwMOETQ4faVaqDkIaA
State: test_1752719403
授权 URL: http://localhost:3001/oauth/authorize?response_type=code&client_id=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcxOTQwMywiaWF0IjoxNzUyNzE5NDAzfQ.D55Y9iOBbi86uUL9Z9z15LCK3Vq-pBoktKM5z6LYx5I&redirect_uri=http%3A//localhost%3A8080/callback&scope=read%20write%20execute&state=test_1752719403&code_challenge=vl6bXuynzJsyMXdk4OeS662orxwMOETQ4faVaqDkIaA&code_challenge_method=S256

下一步操作:
1. 在浏览器中访问授权 URL
2. 使用测试账号登录
3. 授权后从重定向 URL 中复制授权码
4. 运行: ./exchange-token.sh <授权码>
