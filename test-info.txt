测试信息
========
客户端 ID: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcyMTAwMiwiaWF0IjoxNzUyNzIxMDAyfQ.Cl6KBpgp9R8_s0x9_4JSIBBu4cT1Upxr0WZ-24mrvKg
Code Verifier: YFZly3Ixx4QFcdX1MHcKgGw5jIeTvThhFOCb6gXg
Code Challenge: g-05jFmcLEJmxWeY3jUbLX1qEk1oMdltIYEFoh1p32Y
State: test_1752721002
授权 URL: http://localhost:3001/oauth/authorize?response_type=code&client_id=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcyMTAwMiwiaWF0IjoxNzUyNzIxMDAyfQ.Cl6KBpgp9R8_s0x9_4JSIBBu4cT1Upxr0WZ-24mrvKg&redirect_uri=http%3A//localhost%3A8080/callback&scope=read%20write%20execute&state=test_1752721002&code_challenge=g-05jFmcLEJmxWeY3jUbLX1qEk1oMdltIYEFoh1p32Y&code_challenge_method=S256

下一步操作:
1. 在浏览器中访问授权 URL
2. 使用测试账号登录
3. 授权后从重定向 URL 中复制授权码
4. 运行: ./exchange-token.sh <授权码>
