测试信息
========
客户端 ID: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcxNjMzMCwiaWF0IjoxNzUyNzE2MzMwfQ._zY_EE54HLaFHWJlk27Mu0FyF6YLPUameRxk_ZiDzfo
Code Verifier: cElYhymEyUQgMq1rSPpPdQPsVIXMXwKg1p6JGXL7jbM
Code Challenge: 4FsvdOmIydsv5JNCjGYW9z0cvDNlzdCZU3hhNO5Z1_U
State: test_1752716330
授权 URL: http://localhost:3001/oauth/authorize?response_type=code&client_id=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcxNjMzMCwiaWF0IjoxNzUyNzE2MzMwfQ._zY_EE54HLaFHWJlk27Mu0FyF6YLPUameRxk_ZiDzfo&redirect_uri=http%3A//localhost%3A8080/callback&scope=read%20write%20execute&state=test_1752716330&code_challenge=4FsvdOmIydsv5JNCjGYW9z0cvDNlzdCZU3hhNO5Z1_U&code_challenge_method=S256

下一步操作:
1. 在浏览器中访问授权 URL
2. 使用测试账号登录
3. 授权后从重定向 URL 中复制授权码
4. 运行: ./exchange-token.sh <授权码>
