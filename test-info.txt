测试信息
========
客户端 ID: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcxNjcyNiwiaWF0IjoxNzUyNzE2NzI2fQ.U0nccv_U35Jw01gjqFQaWWEDnYx86jRbM6lvmrao9Ns
Code Verifier: 8cUoYytxCUA5ee8NJOw6HkGKqTAISoAZomNVLseMnA
Code Challenge: kl2J7o6Fxs2dEcRFhE8uRR2GxnqKyLOQZXE2gfV8p9s
State: test_1752716726
授权 URL: http://localhost:3001/oauth/authorize?response_type=code&client_id=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyZWRpcmVjdF91cmlzIjpbImh0dHA6Ly9sb2NhbGhvc3Q6ODA4MC9jYWxsYmFjayJdLCJjbGllbnRfbmFtZSI6Iua1i-ivleWuouaIt-erryIsImlzc3VlZF9hdCI6MTc1MjcxNjcyNiwiaWF0IjoxNzUyNzE2NzI2fQ.U0nccv_U35Jw01gjqFQaWWEDnYx86jRbM6lvmrao9Ns&redirect_uri=http%3A//localhost%3A8080/callback&scope=read%20write%20execute&state=test_1752716726&code_challenge=kl2J7o6Fxs2dEcRFhE8uRR2GxnqKyLOQZXE2gfV8p9s&code_challenge_method=S256

下一步操作:
1. 在浏览器中访问授权 URL
2. 使用测试账号登录
3. 授权后从重定向 URL 中复制授权码
4. 运行: ./exchange-token.sh <授权码>
