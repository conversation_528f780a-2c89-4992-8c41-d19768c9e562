#!/usr/bin/env node

/**
 * 简单的 MCP 客户端示例
 * 演示如何通过 HTTP 连接到我们的 MCP OAuth 服务器
 */

const https = require('http');

class MCPClient {
  constructor(baseUrl, accessToken) {
    this.baseUrl = baseUrl;
    this.accessToken = accessToken;
    this.requestId = 1;
  }

  async request(method, params = {}) {
    const payload = {
      jsonrpc: "2.0",
      id: this.requestId++,
      method: method,
      params: params
    };

    return new Promise((resolve, reject) => {
      const data = JSON.stringify(payload);
      
      const options = {
        hostname: 'localhost',
        port: 3002,
        path: '/mcp',
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Length': Buffer.byteLength(data)
        }
      };

      const req = https.request(options, (res) => {
        let responseData = '';
        
        res.on('data', (chunk) => {
          responseData += chunk;
        });
        
        res.on('end', () => {
          try {
            const response = JSON.parse(responseData);
            if (response.error) {
              reject(new Error(`MCP Error: ${response.error.message}`));
            } else {
              resolve(response.result);
            }
          } catch (error) {
            reject(new Error(`Parse error: ${error.message}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.write(data);
      req.end();
    });
  }

  async initialize() {
    return this.request('initialize', {
      protocolVersion: "2024-11-05",
      capabilities: {},
      clientInfo: {
        name: "Simple MCP Client",
        version: "1.0.0"
      }
    });
  }

  async listTools() {
    return this.request('tools/list');
  }

  async callTool(name, args = {}) {
    return this.request('tools/call', {
      name: name,
      arguments: args
    });
  }

  async listResources() {
    return this.request('resources/list');
  }

  async readResource(uri) {
    return this.request('resources/read', { uri });
  }

  async listPrompts() {
    return this.request('prompts/list');
  }
}

async function main() {
  // 从环境变量或文件读取访问令牌
  let accessToken = process.env.ACCESS_TOKEN;
  
  if (!accessToken) {
    try {
      const fs = require('fs');
      const envTest = fs.readFileSync('.env.test', 'utf8');
      const match = envTest.match(/ACCESS_TOKEN=(.+)/);
      if (match) {
        accessToken = match[1];
      }
    } catch (error) {
      console.error('❌ 无法找到访问令牌');
      console.error('请设置环境变量 ACCESS_TOKEN 或运行 ./exchange-token.sh 获取令牌');
      process.exit(1);
    }
  }

  if (!accessToken) {
    console.error('❌ 未提供访问令牌');
    console.error('用法: ACCESS_TOKEN=your_token node mcp-client-example.js');
    process.exit(1);
  }

  console.log('🚀 连接到 MCP OAuth 服务器...');
  
  const client = new MCPClient('http://localhost:3002', accessToken);

  try {
    // 初始化连接
    console.log('\n📡 初始化 MCP 连接...');
    const initResult = await client.initialize();
    console.log('✅ 连接成功:', JSON.stringify(initResult, null, 2));

    // 列出可用工具
    console.log('\n🔧 获取可用工具...');
    const tools = await client.listTools();
    console.log('可用工具:', JSON.stringify(tools, null, 2));

    // 测试 echo 工具
    console.log('\n🔊 测试 echo 工具...');
    const echoResult = await client.callTool('echo', {
      message: 'Hello from MCP Client!'
    });
    console.log('Echo 结果:', JSON.stringify(echoResult, null, 2));

    // 测试用户信息工具
    console.log('\n👤 获取用户信息...');
    const userInfo = await client.callTool('get_user_info');
    console.log('用户信息:', JSON.stringify(userInfo, null, 2));

    // 测试计算器工具
    console.log('\n🧮 测试计算器...');
    const calcResult = await client.callTool('calculator', {
      operation: 'multiply',
      a: 7,
      b: 6
    });
    console.log('计算结果:', JSON.stringify(calcResult, null, 2));

    // 列出资源
    console.log('\n📚 获取可用资源...');
    const resources = await client.listResources();
    console.log('可用资源:', JSON.stringify(resources, null, 2));

    // 读取问候资源
    console.log('\n👋 读取问候资源...');
    const greeting = await client.readResource('greeting://MCP-Client');
    console.log('问候消息:', JSON.stringify(greeting, null, 2));

    // 列出提示
    console.log('\n💭 获取可用提示...');
    const prompts = await client.listPrompts();
    console.log('可用提示:', JSON.stringify(prompts, null, 2));

    console.log('\n🎉 MCP 客户端测试完成！');

  } catch (error) {
    console.error('❌ MCP 客户端错误:', error.message);
    process.exit(1);
  }
}

// 运行客户端
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { MCPClient };
