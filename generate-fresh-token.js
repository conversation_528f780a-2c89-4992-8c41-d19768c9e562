const jwt = require('jsonwebtoken');

// 生成一个新的访问令牌
const payload = {
  sub: 'zhang_san',
  aud: 'test-client',
  scope: 'read write execute',
  exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1小时后过期
  iat: Math.floor(Date.now() / 1000)
};

const token = jwt.sign(payload, 'your-jwt-secret-here-change-in-production');

console.log('✅ Fresh access token generated:');
console.log(token);
console.log('');
console.log('Expires at:', new Date((payload.exp) * 1000).toISOString());
console.log('');
console.log('Save this to .env.test:');
console.log(`ACCESS_TOKEN=${token}`);

// 写入文件
const fs = require('fs');
fs.writeFileSync('.env.test', `ACCESS_TOKEN=${token}\n`);
console.log('✅ Token saved to .env.test');
