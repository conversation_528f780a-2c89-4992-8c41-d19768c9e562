#!/bin/bash

# 简化的 MCP OAuth 测试脚本
# 这个脚本演示基本的 API 端点测试

echo "🧪 MCP OAuth 基础功能测试"
echo "=========================="

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

AUTH_SERVER="http://localhost:3001"
MCP_SERVER="http://localhost:3002"

echo -e "${BLUE}1. 测试服务器健康状态${NC}"
echo "授权服务器:"
curl -s "$AUTH_SERVER/health" | jq .

echo -e "\nMCP 服务器:"
curl -s "$MCP_SERVER/health" | jq .

echo -e "\n${BLUE}2. 测试服务器元数据${NC}"
echo "授权服务器元数据:"
curl -s "$AUTH_SERVER/.well-known/oauth-authorization-server" | jq .

echo -e "\n资源服务器元数据:"
curl -s "$MCP_SERVER/.well-known/oauth-protected-resource" | jq .

echo -e "\n${BLUE}3. 测试客户端注册${NC}"
CLIENT_RESPONSE=$(curl -s -X POST "$AUTH_SERVER/oauth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "client_name": "测试客户端",
    "redirect_uris": ["http://localhost:8080/callback"]
  }')

echo "客户端注册响应:"
echo "$CLIENT_RESPONSE" | jq .

CLIENT_ID=$(echo "$CLIENT_RESPONSE" | jq -r .client_id)
echo -e "\n${GREEN}✅ 客户端注册成功${NC}"

echo -e "\n${BLUE}4. 测试无令牌的 MCP 访问 (应该返回 401)${NC}"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/list"
  }' | jq .

echo -e "\n${BLUE}5. 生成授权 URL${NC}"
CODE_VERIFIER=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-43)
CODE_CHALLENGE=$(echo -n "$CODE_VERIFIER" | openssl dgst -sha256 -binary | openssl base64 | tr -d "=" | tr "/+" "_-")
STATE="test_$(date +%s)"

# URL 编码 CLIENT_ID
ENCODED_CLIENT_ID=$(echo "$CLIENT_ID" | sed 's/+/%2B/g' | sed 's/\//%2F/g' | sed 's/=/%3D/g')

AUTHORIZE_URL="$AUTH_SERVER/oauth/authorize?response_type=code&client_id=$ENCODED_CLIENT_ID&redirect_uri=http%3A//localhost%3A8080/callback&scope=read%20write%20execute&state=$STATE&code_challenge=$CODE_CHALLENGE&code_challenge_method=S256"

echo "请在浏览器中访问以下 URL 进行授权:"
echo "$AUTHORIZE_URL"

echo -e "\n${GREEN}测试用户账号:${NC}"
echo "• zhang_san / dev123 (权限: read,write,execute)"
echo "• li_si / pm456 (权限: read)"  
echo "• admin / admin789 (权限: read,write,execute,admin)"

echo -e "\n${BLUE}6. 保存测试信息${NC}"
cat > test-info.txt << EOF
测试信息
========
客户端 ID: $CLIENT_ID
Code Verifier: $CODE_VERIFIER
Code Challenge: $CODE_CHALLENGE
State: $STATE
授权 URL: $AUTHORIZE_URL

下一步操作:
1. 在浏览器中访问授权 URL
2. 使用测试账号登录
3. 授权后从重定向 URL 中复制授权码
4. 运行: ./exchange-token.sh <授权码>
EOF

echo "测试信息已保存到 test-info.txt"
echo -e "${GREEN}✅ 基础功能测试完成${NC}"
