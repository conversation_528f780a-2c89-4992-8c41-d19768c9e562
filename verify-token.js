const jwt = require('jsonwebtoken');

const token = process.argv[2] || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJ6aGFuZ19zYW4iLCJhdWQiOiJleUpoYkdjaU9pSklVekkxTmlJc0luUjVjQ0k2SWtwWFZDSjkuZXlKeVpXUnBjbVZqZEY5MWNtbHpJanBiSW1oMGRIQTZMeTlzYjJOaGJHaHZjM1E2TmpJM05DOXZZWFYwYUM5allXeHNZbUZqYXk5a1pXSjFaeUpkTENKamJHbGxiblJmYm1GdFpTSTZJazFEVUNCSmJuTndaV04wYjNJaUxDSnBjM04xWldSZllYUWlPakUzTlRJM01qUXpNelVzSW1saGRDSTZNVGMxTWpjeU5ETXpOWDAuM2tJeUgzeFNsTGFaeEVXQXdraW1GUzV0bWJEaC1YdVVFRVlkdUJBenhXcyIsInNjb3BlIjoicmVhZCB3cml0ZSBleGVjdXRlIiwiZXhwIjoxNzUyNzI3OTM4LCJpYXQiOjE3NTI3MjQzMzh9.uoFCmkjmmp3_eAbUQQH_GMT0dtP-yhJVnhVq--IHkZI';

console.log('Token:', token);
console.log('');

try {
  // 解码不验证签名
  const decoded = jwt.decode(token, { complete: true });
  console.log('Decoded header:', decoded.header);
  console.log('Decoded payload:', decoded.payload);
  
  const now = Math.floor(Date.now() / 1000);
  console.log('Current time:', now);
  console.log('Token exp:', decoded.payload.exp);
  console.log('Token iat:', decoded.payload.iat);
  console.log('Is expired:', now > decoded.payload.exp);
  
  // 尝试验证签名
  try {
    const verified = jwt.verify(token, 'your-jwt-secret-here-change-in-production');
    console.log('✅ Token signature is valid');
    console.log('Verified payload:', verified);
  } catch (verifyError) {
    console.log('❌ Token signature verification failed:', verifyError.message);
  }
  
} catch (error) {
  console.error('❌ Failed to decode token:', error.message);
}
