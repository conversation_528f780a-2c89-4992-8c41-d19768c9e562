#!/bin/bash

# Setup Cursor MCP Configuration Script
# This script configures Cursor to use our MCP OAuth server

set -e

echo "🎯 Setting up Cursor MCP Configuration..."

# 检查当前访问令牌
if [ ! -f ".env.test" ]; then
    echo "❌ Error: .env.test file not found. Please run ./simple-test.sh first to get an access token."
    exit 1
fi

# 读取访问令牌
ACCESS_TOKEN=$(grep "ACCESS_TOKEN=" .env.test | cut -d'=' -f2)

if [ -z "$ACCESS_TOKEN" ]; then
    echo "❌ Error: No access token found in .env.test. Please run ./simple-test.sh first."
    exit 1
fi

echo "✅ Found access token: ${ACCESS_TOKEN:0:20}..."

# 获取当前项目路径
PROJECT_PATH=$(pwd)
BRIDGE_SCRIPT="$PROJECT_PATH/mcp-http-bridge.js"

# 确保桥接脚本可执行
chmod +x "$BRIDGE_SCRIPT"

# 检测操作系统并设置 Cursor 配置路径
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    CURSOR_CONFIG_DIR="$HOME/Library/Application Support/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    # Linux
    CURSOR_CONFIG_DIR="$HOME/.config/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "win32" ]]; then
    # Windows
    CURSOR_CONFIG_DIR="$APPDATA/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
else
    echo "❌ Unsupported operating system: $OSTYPE"
    exit 1
fi

echo "📁 Cursor config directory: $CURSOR_CONFIG_DIR"

# 创建配置目录（如果不存在）
mkdir -p "$CURSOR_CONFIG_DIR"

# 创建 MCP 配置文件
CONFIG_FILE="$CURSOR_CONFIG_DIR/cline_mcp_settings.json"

cat > "$CONFIG_FILE" << EOF
{
  "mcpServers": {
    "mcp-oauth-server": {
      "command": "node",
      "args": ["$BRIDGE_SCRIPT"],
      "env": {
        "MCP_SERVER_URL": "http://localhost:3002/mcp",
        "ACCESS_TOKEN": "$ACCESS_TOKEN"
      }
    }
  }
}
EOF

echo "✅ Created Cursor MCP configuration: $CONFIG_FILE"

# 显示配置内容
echo ""
echo "📋 Configuration created:"
echo "----------------------------------------"
cat "$CONFIG_FILE"
echo "----------------------------------------"

echo ""
echo "🎉 Setup complete!"
echo ""
echo "📝 Next steps:"
echo "1. 确保 MCP 服务器正在运行: npm run dev"
echo "2. 重启 Cursor"
echo "3. 在 Cursor 中，MCP 服务器应该会自动连接"
echo "4. 你可以使用以下工具:"
echo "   - echo: 回显消息"
echo "   - get_user_info: 获取用户信息"
echo "   - calculator: 计算器"
echo "   - system_info: 系统信息 (需要 admin 权限)"
echo ""
echo "🔄 如果访问令牌过期，请运行:"
echo "   ./simple-test.sh"
echo "   ./setup-cursor-mcp.sh"
echo ""
echo "🐛 如果遇到问题，请检查:"
echo "   - MCP 服务器是否在运行 (http://localhost:3002/health)"
echo "   - 访问令牌是否有效"
echo "   - Cursor 是否已重启"
