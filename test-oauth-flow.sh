#!/bin/bash

# MCP OAuth 2.1 完整流程测试脚本
# 这个脚本演示了完整的 OAuth 2.1 授权码流程

set -e

echo "🚀 MCP OAuth 2.1 流程测试开始"
echo "================================"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 服务器地址
AUTH_SERVER="http://localhost:3001"
MCP_SERVER="http://localhost:3002"

echo -e "${BLUE}步骤 1: 检查服务器状态${NC}"
echo "检查授权服务器..."
curl -s "$AUTH_SERVER/health" | jq .
echo "检查 MCP 服务器..."
curl -s "$MCP_SERVER/health" | jq .

echo -e "\n${BLUE}步骤 2: 获取服务器元数据${NC}"
echo "授权服务器元数据:"
curl -s "$AUTH_SERVER/.well-known/oauth-authorization-server" | jq .

echo -e "\n资源服务器元数据:"
curl -s "$MCP_SERVER/.well-known/oauth-protected-resource" | jq .

echo -e "\n${BLUE}步骤 3: 动态客户端注册${NC}"
CLIENT_RESPONSE=$(curl -s -X POST "$AUTH_SERVER/oauth/register" \
  -H "Content-Type: application/json" \
  -d '{
    "client_name": "测试 MCP 客户端",
    "redirect_uris": ["http://localhost:8080/callback"]
  }')

echo "客户端注册响应:"
echo "$CLIENT_RESPONSE" | jq .

CLIENT_ID=$(echo "$CLIENT_RESPONSE" | jq -r .client_id)
echo -e "\n${GREEN}客户端 ID: $CLIENT_ID${NC}"

echo -e "\n${BLUE}步骤 4: 生成 PKCE 参数${NC}"
# 生成 code_verifier (43-128 字符的随机字符串)
CODE_VERIFIER=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-43)
echo "Code Verifier: $CODE_VERIFIER"

# 生成 code_challenge (SHA256 + Base64URL)
CODE_CHALLENGE=$(echo -n "$CODE_VERIFIER" | openssl dgst -sha256 -binary | openssl base64 | tr -d "=" | tr "/+" "_-")
echo "Code Challenge: $CODE_CHALLENGE"

echo -e "\n${BLUE}步骤 5: 构建授权 URL${NC}"
STATE="test_state_$(date +%s)"
SCOPE="read write execute"
REDIRECT_URI="http://localhost:8080/callback"

AUTHORIZE_URL="$AUTH_SERVER/oauth/authorize?response_type=code&client_id=$(echo "$CLIENT_ID" | sed 's/+/%2B/g' | sed 's/\//%2F/g' | sed 's/=/%3D/g')&redirect_uri=$REDIRECT_URI&scope=$(echo "$SCOPE" | sed 's/ /%20/g')&state=$STATE&code_challenge=$CODE_CHALLENGE&code_challenge_method=S256"

echo "授权 URL:"
echo "$AUTHORIZE_URL"

echo -e "\n${YELLOW}请在浏览器中访问上述 URL 进行授权${NC}"
echo -e "${YELLOW}使用以下测试用户之一登录:${NC}"
echo "  - 用户名: zhang_san, 密码: dev123 (权限: read,write,execute)"
echo "  - 用户名: li_si, 密码: pm456 (权限: read)"
echo "  - 用户名: admin, 密码: admin789 (权限: read,write,execute,admin)"

echo -e "\n${YELLOW}授权后，请输入重定向 URL 中的授权码:${NC}"
read -p "授权码 (code): " AUTH_CODE

if [ -z "$AUTH_CODE" ]; then
  echo -e "${RED}错误: 未提供授权码${NC}"
  exit 1
fi

echo -e "\n${BLUE}步骤 6: 交换访问令牌${NC}"
TOKEN_RESPONSE=$(curl -s -X POST "$AUTH_SERVER/oauth/token" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "grant_type=authorization_code&code=$AUTH_CODE&redirect_uri=$REDIRECT_URI&client_id=$CLIENT_ID&code_verifier=$CODE_VERIFIER")

echo "令牌响应:"
echo "$TOKEN_RESPONSE" | jq .

ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r .access_token)

if [ "$ACCESS_TOKEN" = "null" ] || [ -z "$ACCESS_TOKEN" ]; then
  echo -e "${RED}错误: 无法获取访问令牌${NC}"
  echo "响应: $TOKEN_RESPONSE"
  exit 1
fi

echo -e "\n${GREEN}访问令牌获取成功!${NC}"

echo -e "\n${BLUE}步骤 7: 测试 MCP 功能${NC}"

echo -e "\n7.1 测试工具列表:"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/list"
  }' | jq .

echo -e "\n7.2 测试 echo 工具:"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 2,
    "method": "tools/call",
    "params": {
      "name": "echo",
      "arguments": {
        "message": "Hello from MCP OAuth!"
      }
    }
  }' | jq .

echo -e "\n7.3 测试用户信息工具:"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 3,
    "method": "tools/call",
    "params": {
      "name": "get_user_info",
      "arguments": {}
    }
  }' | jq .

echo -e "\n7.4 测试计算器工具:"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 4,
    "method": "tools/call",
    "params": {
      "name": "calculator",
      "arguments": {
        "operation": "add",
        "a": 15,
        "b": 27
      }
    }
  }' | jq .

echo -e "\n7.5 测试资源列表:"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 5,
    "method": "resources/list"
  }' | jq .

echo -e "\n7.6 测试资源读取:"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 6,
    "method": "resources/read",
    "params": {
      "uri": "greeting://World"
    }
  }' | jq .

echo -e "\n7.7 测试提示列表:"
curl -s -X POST "$MCP_SERVER/mcp" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "jsonrpc": "2.0",
    "id": 7,
    "method": "prompts/list"
  }' | jq .

echo -e "\n${GREEN}🎉 MCP OAuth 2.1 流程测试完成!${NC}"
echo "================================"
