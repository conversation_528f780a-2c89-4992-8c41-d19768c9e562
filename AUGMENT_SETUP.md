# 将 MCP OAuth 服务器添加到 Augment

本指南将帮你将我们的 MCP OAuth 服务器添加到 Augment 的 MCP 配置中。

## 🚀 快速设置

### 1. 启动 MCP OAuth 服务器

确保服务器正在运行：

```bash
cd /Users/<USER>/Documents/augment-projects/linear-oldc
npm run dev
```

服务器应该在以下端口运行：
- **认证服务器**: http://localhost:3001
- **MCP 服务器**: http://localhost:3002

### 2. 获取访问令牌

运行 OAuth 流程获取访问令牌：

```bash
./simple-test.sh
```

然后在浏览器中完成授权（使用 `admin` / `admin789` 获得完整权限），并运行：

```bash
./exchange-token.sh <授权码>
```

访问令牌将保存在 `.env.test` 文件中。

### 3. 配置 Augment MCP

在 Augment 的 MCP 配置文件中添加以下配置：

#### 方法 A: HTTP 传输（推荐）

```json
{
  "mcpServers": {
    "oauth-demo-server": {
      "transport": {
        "type": "http",
        "url": "http://localhost:3002/mcp",
        "headers": {
          "Authorization": "Bearer YOUR_ACCESS_TOKEN_HERE"
        }
      },
      "description": "MCP OAuth Demo Server with authentication",
      "capabilities": {
        "tools": true,
        "resources": true,
        "prompts": true
      }
    }
  }
}
```

**替换 `YOUR_ACCESS_TOKEN_HERE` 为实际的访问令牌**

#### 方法 B: 如果 Augment 支持 OAuth 自动化

```json
{
  "mcpServers": {
    "oauth-demo-server": {
      "transport": {
        "type": "http",
        "url": "http://localhost:3002/mcp",
        "oauth": {
          "authorizationServer": "http://localhost:3001",
          "clientId": "augment-client",
          "scopes": ["read", "write", "execute", "admin"]
        }
      },
      "description": "MCP OAuth Demo Server with OAuth authentication"
    }
  }
}
```

## 🛠️ 可用功能

连接成功后，你将可以使用以下 MCP 功能：

### 工具 (Tools)
- **echo** - 回显消息
- **get_user_info** - 获取当前用户信息 (需要 read 权限)
- **calculator** - 基础计算器 (需要 execute 权限)
- **system_info** - 系统信息 (需要 admin 权限)

### 资源 (Resources)
- **greeting://{name}** - 个性化问候资源

### 提示 (Prompts)
- **user_assistant** - 用户助手提示

## 🔐 用户权限

- **admin** / **admin789** - 完整权限 (read, write, execute, admin)
- **zhang_san** / **dev123** - 开发权限 (read, write, execute)
- **li_si** / **pm456** - 只读权限 (read)

## 🧪 测试连接

配置完成后，你可以在 Augment 中测试以下功能：

1. **Echo 测试**：
   ```
   使用 echo 工具发送消息 "Hello from Augment!"
   ```

2. **用户信息**：
   ```
   调用 get_user_info 工具查看当前认证用户
   ```

3. **计算器**：
   ```
   使用 calculator 工具执行 add 操作：a=10, b=20
   ```

4. **系统信息** (仅 admin 用户)：
   ```
   调用 system_info 工具查看服务器状态
   ```

## 🔧 故障排除

### 问题 1: 连接失败
- 确保 MCP OAuth 服务器正在运行
- 检查端口 3001 和 3002 是否可访问
- 验证访问令牌是否有效（令牌有效期 1 小时）

### 问题 2: 认证错误
- 重新获取访问令牌
- 确保使用正确的用户权限
- 检查令牌是否包含所需的 scopes

### 问题 3: 权限不足
- 使用 `admin` 用户获取完整权限
- 检查用户的 scopes 配置
- 确认工具所需的权限级别

## 📝 配置文件位置

Augment 的 MCP 配置文件通常位于：
- **macOS**: `~/Library/Application Support/Augment/mcp.json`
- **Windows**: `%APPDATA%\Augment\mcp.json`
- **Linux**: `~/.config/Augment/mcp.json`

## 🔄 令牌刷新

访问令牌有效期为 1 小时。当令牌过期时：

1. 重新运行 OAuth 流程获取新令牌
2. 更新 Augment 配置中的 Authorization header
3. 重启 Augment 或重新加载 MCP 配置

## 📞 支持

如果遇到问题，请检查：
1. MCP OAuth 服务器日志
2. Augment 的 MCP 连接日志
3. 网络连接和端口可访问性
