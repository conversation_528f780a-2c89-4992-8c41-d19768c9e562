import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import { randomUUID } from 'node:crypto';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { isInitializeRequest } from '@modelcontextprotocol/sdk/types.js';
import { z } from 'zod';
import { Config } from './config';
import { JWTUtils } from './jwt-utils';
import { OAuthHandler } from './oauth-handler';
import { AccessToken } from './types';

export class MCPOfficialServer {
  private app: Express;
  private server: any;
  private transports: { [sessionId: string]: StreamableHTTPServerTransport } = {};

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    // CORS 配置 - 允许 MCP Inspector 连接
    this.app.use(cors({
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'mcp-session-id'],
      exposedHeaders: ['Mcp-Session-Id']
    }));

    // 解析 JSON
    this.app.use(express.json());
  }

  private setupRoutes(): void {
    // 资源服务器元数据端点
    this.app.get('/.well-known/oauth-protected-resource', (req: Request, res: Response) => {
      res.json(OAuthHandler.getResourceServerMetadata());
    });

    // MCP Streamable HTTP 端点
    this.app.all('/mcp', async (req: Request, res: Response) => {
      await this.handleMCPRequest(req, res);
    });

    // 健康检查端点
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'mcp-official-server'
      });
    });

    // 错误处理中间件
    this.app.use((err: any, req: Request, res: Response, next: any) => {
      console.error('MCP official server error:', err);
      res.status(500).json({
        error: 'server_error',
        error_description: 'Internal server error'
      });
    });

    // 404 处理
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        error: 'not_found',
        error_description: 'Endpoint not found'
      });
    });
  }

  private async handleMCPRequest(req: Request, res: Response): Promise<void> {
    try {
      // 验证访问令牌
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1];

      if (!token) {
        res.status(401).set({
          'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
        }).json({
          error: 'invalid_token',
          error_description: 'Missing access token'
        });
        return;
      }

      let user: AccessToken;
      try {
        user = JWTUtils.verifyAccessToken(token);
      } catch (error) {
        res.status(401).set({
          'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
        }).json({
          error: 'invalid_token',
          error_description: 'Invalid or expired access token'
        });
        return;
      }

      console.log(`📨 MCP request from ${user.sub}`);

      // 检查现有会话 ID
      const sessionId = req.headers['mcp-session-id'] as string | undefined;
      let transport: StreamableHTTPServerTransport;

      if (sessionId && this.transports[sessionId]) {
        // 重用现有传输
        transport = this.transports[sessionId];
      } else if (!sessionId && isInitializeRequest(req.body)) {
        // 新的初始化请求
        transport = new StreamableHTTPServerTransport({
          sessionIdGenerator: () => randomUUID(),
          onsessioninitialized: (sessionId) => {
            // 存储传输
            this.transports[sessionId] = transport;
            console.log(`📋 New MCP session: ${sessionId}`);
          },
          enableDnsRebindingProtection: true,
          allowedHosts: ['127.0.0.1', 'localhost']
        });

        // 清理传输
        transport.onclose = () => {
          if (transport.sessionId) {
            delete this.transports[transport.sessionId];
            console.log(`🗑️ Closed MCP session: ${transport.sessionId}`);
          }
        };

        // 创建 MCP 服务器实例
        const mcpServer = this.createMCPServer(user);
        
        // 连接到 MCP 服务器
        await mcpServer.connect(transport);
      } else {
        // 无效请求
        res.status(400).json({
          jsonrpc: '2.0',
          error: {
            code: -32000,
            message: 'Bad Request: No valid session ID provided',
          },
          id: null,
        });
        return;
      }

      // 处理请求
      await transport.handleRequest(req, res, req.body);
    } catch (error) {
      console.error('MCP request error:', error);
      res.status(500).json({
        jsonrpc: '2.0',
        error: {
          code: -32603,
          message: 'Internal error',
          data: String(error)
        },
        id: null
      });
    }
  }

  private createMCPServer(user: AccessToken): McpServer {
    const server = new McpServer({
      name: 'MCP OAuth Server',
      version: '1.0.0'
    });

    const userScopes = user.scope.split(' ');

    // Echo 工具 - 所有用户都可以使用
    server.registerTool(
      'echo',
      {
        title: 'Echo Tool',
        description: 'Echo back the input message',
        inputSchema: {
          message: z.string().describe('Message to echo back')
        }
      },
      async ({ message }) => ({
        content: [{
          type: 'text',
          text: `Echo: ${message}`
        }]
      })
    );

    // 用户信息工具 - 需要 read 权限
    if (userScopes.includes('read')) {
      server.registerTool(
        'get_user_info',
        {
          title: 'Get User Info',
          description: 'Get current authenticated user information',
          inputSchema: {}
        },
        async () => ({
          content: [{
            type: 'text',
            text: JSON.stringify({
              user: user.sub,
              scopes: userScopes,
              client: user.aud,
              expires: new Date(user.exp * 1000).toISOString()
            }, null, 2)
          }]
        })
      );
    }

    // 计算器工具 - 需要 execute 权限
    if (userScopes.includes('execute')) {
      server.registerTool(
        'calculator',
        {
          title: 'Calculator',
          description: 'Perform basic arithmetic operations',
          inputSchema: {
            operation: z.enum(['add', 'subtract', 'multiply', 'divide']).describe('The arithmetic operation to perform'),
            a: z.number().describe('First number'),
            b: z.number().describe('Second number')
          }
        },
        async ({ operation, a, b }) => {
          let result: number;
          switch (operation) {
            case 'add':
              result = a + b;
              break;
            case 'subtract':
              result = a - b;
              break;
            case 'multiply':
              result = a * b;
              break;
            case 'divide':
              if (b === 0) {
                throw new Error('Division by zero is not allowed');
              }
              result = a / b;
              break;
            default:
              throw new Error(`Unknown operation: ${operation}`);
          }

          return {
            content: [{
              type: 'text',
              text: `${a} ${operation} ${b} = ${result}`
            }]
          };
        }
      );
    }

    // 系统信息工具 - 需要 admin 权限
    if (userScopes.includes('admin')) {
      server.registerTool(
        'system_info',
        {
          title: 'System Info',
          description: 'Get system and server information (admin only)',
          inputSchema: {}
        },
        async () => ({
          content: [{
            type: 'text',
            text: JSON.stringify({
              server: {
                name: 'MCP OAuth Server',
                version: '1.0.0',
                auth_server_url: Config.AUTH_SERVER_URL,
                mcp_server_url: Config.MCP_SERVER_URL
              },
              environment: {
                node_env: Config.NODE_ENV,
                is_development: Config.IS_DEVELOPMENT
              },
              user: {
                authenticated_as: user.sub,
                scopes: userScopes
              }
            }, null, 2)
          }]
        })
      );
    }

    // 添加资源
    server.registerResource(
      'greeting',
      'greeting://world',
      {
        title: 'Greeting Resource',
        description: 'A simple greeting resource',
        mimeType: 'text/plain'
      },
      async (uri) => ({
        contents: [{
          uri: uri.href,
          mimeType: 'text/plain',
          text: `Hello, world! You are authenticated as ${user.sub} with scopes: ${userScopes.join(', ')}.`
        }]
      })
    );

    // 添加提示
    server.registerPrompt(
      'user_assistant',
      {
        title: 'User Assistant',
        description: 'A helpful assistant for the authenticated user',
        argsSchema: {}
      },
      () => ({
        messages: [{
          role: 'user',
          content: {
            type: 'text',
            text: `You are a helpful assistant for ${user.sub}. The user has permissions: ${userScopes.join(', ')}.`
          }
        }]
      })
    );

    return server;
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(Config.MCP_SERVER_PORT, () => {
          console.log(`🔧 MCP Official Server listening on port ${Config.MCP_SERVER_PORT}`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          if (error.code === 'EADDRINUSE') {
            reject(new Error(`Port ${Config.MCP_SERVER_PORT} is already in use`));
          } else {
            reject(error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      // 关闭所有传输
      Object.values(this.transports).forEach(transport => {
        transport.close();
      });
      this.transports = {};

      if (this.server) {
        this.server.close(() => {
          console.log('🔧 MCP Official Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}
