import jwt from 'jsonwebtoken';
import { Config } from './config';
import { AuthorizationCode, AccessToken } from './types';

export class JWTUtils {
  /**
   * 生成授权码 JWT
   */
  static generateAuthorizationCode(payload: Omit<AuthorizationCode, 'exp'>): string {
    const authCode: AuthorizationCode = {
      ...payload,
      exp: Math.floor(Date.now() / 1000) + Config.AUTH_CODE_EXPIRES_IN
    };
    
    return jwt.sign(authCode, Config.AUTH_CODE_SECRET, {
      algorithm: 'HS256'
    });
  }
  
  /**
   * 验证并解析授权码 JWT
   */
  static verifyAuthorizationCode(code: string): AuthorizationCode {
    try {
      const decoded = jwt.verify(code, Config.AUTH_CODE_SECRET) as AuthorizationCode;
      
      // 检查是否过期
      if (decoded.exp < Math.floor(Date.now() / 1000)) {
        throw new Error('Authorization code expired');
      }
      
      return decoded;
    } catch (error) {
      throw new Error(`Invalid authorization code: ${error}`);
    }
  }
  
  /**
   * 生成访问令牌 JWT
   */
  static generateAccessToken(payload: Omit<AccessToken, 'exp'>): string {
    const accessToken: AccessToken = {
      ...payload,
      exp: Math.floor(Date.now() / 1000) + Config.ACCESS_TOKEN_EXPIRES_IN
    };
    
    return jwt.sign(accessToken, Config.JWT_SECRET, {
      algorithm: 'HS256'
    });
  }
  
  /**
   * 验证并解析访问令牌 JWT
   */
  static verifyAccessToken(token: string): AccessToken {
    try {
      const decoded = jwt.verify(token, Config.JWT_SECRET) as AccessToken;
      
      // 检查是否过期
      if (decoded.exp < Math.floor(Date.now() / 1000)) {
        throw new Error('Access token expired');
      }
      
      return decoded;
    } catch (error) {
      throw new Error(`Invalid access token: ${error}`);
    }
  }
  
  /**
   * 生成客户端 ID（自签名 JWT）
   */
  static generateClientId(clientInfo: {
    redirect_uris: string[];
    client_name?: string | undefined;
    issued_at: number;
  }): string {
    return jwt.sign(clientInfo, Config.CLIENT_SECRET, {
      algorithm: 'HS256'
    });
  }
  
  /**
   * 验证客户端 ID
   */
  static verifyClientId(clientId: string): any {
    try {
      return jwt.verify(clientId, Config.CLIENT_SECRET);
    } catch (error) {
      throw new Error(`Invalid client ID: ${error}`);
    }
  }
  
  /**
   * 验证 PKCE code_verifier
   */
  static verifyPKCE(codeVerifier: string, codeChallenge: string, method: string = 'S256'): boolean {
    if (method !== 'S256') {
      throw new Error('Only S256 code challenge method is supported');
    }
    
    const crypto = require('crypto');
    const hash = crypto.createHash('sha256').update(codeVerifier).digest();
    const base64Hash = hash.toString('base64url');
    
    return base64Hash === codeChallenge;
  }
}
