import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { Config } from './config';
import { JWTUtils } from './jwt-utils';
import { <PERSON><PERSON><PERSON>Hand<PERSON> } from './oauth-handler';
import { MCPSDKServer } from './mcp-sdk-server';
import { UserManager } from './user-manager';
import { MCPRequest, MCPResponse, MCPTool, AccessToken } from './types';

export class MCPServer {
  private app: Express;
  private server: any;
  private mcpSDKServer: MCPSDKServer;

  constructor() {
    this.app = express();
    this.mcpSDKServer = new MCPSDKServer();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    // 安全中间件
    this.app.use(helmet());

    // CORS 配置
    this.app.use(cors({
      origin: Config.IS_DEVELOPMENT ? true : [Config.AUTH_SERVER_URL],
      credentials: true
    }));

    // 解析 JSON
    this.app.use(express.json());

    // Bearer Token 验证中间件
    this.app.use('/mcp', this.authenticateToken.bind(this));
  }

  private authenticateToken(req: Request, res: Response, next: any): void {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
      }).json({
        error: 'invalid_token',
        error_description: 'Missing access token'
      });
      return;
    }

    try {
      const decoded: AccessToken = JWTUtils.verifyAccessToken(token);
      (req as any).user = decoded;
      next();
    } catch (error) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
      }).json({
        error: 'invalid_token',
        error_description: 'Invalid or expired access token'
      });
    }
  }

  private setupRoutes(): void {
    // 资源服务器元数据端点
    this.app.get('/.well-known/oauth-protected-resource', (req: Request, res: Response) => {
      res.json(OAuthHandler.getResourceServerMetadata());
    });

    // MCP Streamable HTTP 传输端点 (需要认证)
    this.app.post('/mcp', (req: Request, res: Response) => {
      this.handleMCPRequest(req, res);
    });

    // MCP Inspector 专用端点 (开发环境下无需认证)
    if (Config.IS_DEVELOPMENT) {
      this.app.post('/mcp-inspector', (req: Request, res: Response) => {
        this.handleMCPInspectorRequest(req, res);
      });
    }

    // SSE 传输端点（兼容性）
    this.app.get('/sse', (req: Request, res: Response) => {
      this.handleSSEConnection(req, res);
    });

    // MCP 工具端点示例
    this.app.get('/mcp/v1/tools', (req: Request, res: Response) => {
      this.handleToolsRequest(req, res);
    });

    // 健康检查端点
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'mcp-server'
      });
    });

    // 错误处理中间件
    this.app.use((err: any, req: Request, res: Response, next: any) => {
      console.error('MCP server error:', err);
      res.status(500).json({
        error: 'server_error',
        error_description: 'Internal server error'
      });
    });

    // 404 处理
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        error: 'not_found',
        error_description: 'Endpoint not found'
      });
    });
  }

  private async handleMCPRequest(req: Request, res: Response): Promise<void> {
    try {
      const mcpRequest: MCPRequest = req.body;
      const user = (req as any).user as AccessToken;

      console.log(`📨 MCP Request from ${user.sub}: ${mcpRequest.method}`);

      // 设置当前用户的访问令牌到 SDK 服务器
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1];
      if (token) {
        this.mcpSDKServer.setAccessToken(token);
      }

      // 验证 MCP 协议版本
      const protocolVersion = req.headers['mcp-protocol-version'];
      if (protocolVersion && protocolVersion !== '2024-11-05') {
        res.status(400).json({
          jsonrpc: '2.0',
          id: mcpRequest.id,
          error: {
            code: -32600,
            message: 'Unsupported MCP protocol version'
          }
        });
        return;
      }

      // 使用 SDK 服务器处理请求
      let response: MCPResponse;

      try {
        // 这里我们需要模拟 SDK 服务器的处理逻辑
        // 因为 SDK 服务器主要设计用于 stdio 传输
        response = await this.handleSDKRequest(mcpRequest, user);
      } catch (error) {
        response = {
          jsonrpc: '2.0',
          id: mcpRequest.id || null,
          error: {
            code: -32603,
            message: `Error: ${error}`,
            data: { method: mcpRequest.method }
          }
        };
      }

      res.json(response);
    } catch (error) {
      console.error('MCP request error:', error);
      res.status(500).json({
        jsonrpc: '2.0',
        id: req.body.id,
        error: {
          code: -32603,
          message: 'Internal error'
        }
      });
    }
  }

  private async handleSDKRequest(request: MCPRequest, user: AccessToken): Promise<MCPResponse> {
    // 这个方法桥接 HTTP 请求到 SDK 服务器功能
    switch (request.method) {
      case 'initialize':
        return this.handleInitialize(request, user);
      case 'tools/list':
        return this.handleToolsList(request, user);
      case 'tools/call':
        return await this.handleToolsCallSDK(request, user);
      case 'resources/list':
        return this.handleResourcesList(request, user);
      case 'resources/read':
        return await this.handleResourcesReadSDK(request, user);
      case 'prompts/list':
        return this.handlePromptsList(request, user);
      case 'prompts/get':
        return await this.handlePromptsGet(request, user);
      default:
        return {
          jsonrpc: '2.0',
          id: request.id || null,
          error: {
            code: -32601,
            message: 'Method not found',
            data: { method: request.method }
          }
        };
    }
  }

  private handleInitialize(request: MCPRequest, user: AccessToken): MCPResponse {
    return {
      jsonrpc: '2.0',
      id: request.id || null,
      result: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
          resources: {},
          prompts: {}
        },
        serverInfo: {
          name: 'MCP OAuth Server',
          version: '1.0.0'
        }
      }
    };
  }

  private handleToolsList(request: MCPRequest, user: AccessToken): MCPResponse {
    const userScopes = user.scope.split(' ');
    const tools: MCPTool[] = [
      {
        name: 'echo',
        description: 'Echo back the input message',
        inputSchema: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'Message to echo back'
            }
          },
          required: ['message']
        }
      },
      {
        name: 'get_user_info',
        description: 'Get current user information (requires read scope)',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'calculator',
        description: 'Perform basic arithmetic operations (requires execute scope)',
        inputSchema: {
          type: 'object',
          properties: {
            operation: {
              type: 'string',
              enum: ['add', 'subtract', 'multiply', 'divide'],
              description: 'The arithmetic operation to perform'
            },
            a: {
              type: 'number',
              description: 'First number'
            },
            b: {
              type: 'number',
              description: 'Second number'
            }
          },
          required: ['operation', 'a', 'b']
        }
      },
      {
        name: 'system_info',
        description: 'Get system and server information (requires admin scope)',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      }
    ];

    // 根据用户权限过滤工具
    const filteredTools = tools.filter(tool => {
      switch (tool.name) {
        case 'get_user_info':
          return userScopes.includes('read');
        case 'calculator':
          return userScopes.includes('execute');
        case 'system_info':
          return userScopes.includes('admin');
        default:
          return true; // echo 工具对所有用户可用
      }
    });

    return {
      jsonrpc: '2.0',
      id: request.id || null,
      result: {
        tools: filteredTools
      }
    };
  }

  private async handleToolsCallSDK(request: MCPRequest, user: AccessToken): Promise<MCPResponse> {
    const { name, arguments: args } = request.params;
    const userScopes = user.scope.split(' ');

    try {
      switch (name) {
        case 'echo':
          return {
            jsonrpc: '2.0',
            id: request.id || null,
            result: {
              content: [
                {
                  type: 'text',
                  text: `Echo: ${args.message}`
                }
              ]
            }
          };

        case 'get_user_info':
          if (!userScopes.includes('read')) {
            return {
              jsonrpc: '2.0',
              id: request.id || null,
              error: {
                code: -32603,
                message: 'Insufficient permissions: read scope required'
              }
            };
          }

          return {
            jsonrpc: '2.0',
            id: request.id || null,
            result: {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    user: user.sub,
                    scopes: userScopes,
                    client: user.aud,
                    expires: new Date(user.exp * 1000).toISOString()
                  }, null, 2)
                }
              ]
            }
          };

        case 'calculator':
          if (!userScopes.includes('execute')) {
            return {
              jsonrpc: '2.0',
              id: request.id || null,
              error: {
                code: -32603,
                message: 'Insufficient permissions: execute scope required'
              }
            };
          }

          const { operation, a, b } = args;
          let result: number;

          switch (operation) {
            case 'add':
              result = a + b;
              break;
            case 'subtract':
              result = a - b;
              break;
            case 'multiply':
              result = a * b;
              break;
            case 'divide':
              if (b === 0) {
                return {
                  jsonrpc: '2.0',
                  id: request.id || null,
                  error: {
                    code: -32602,
                    message: 'Division by zero is not allowed'
                  }
                };
              }
              result = a / b;
              break;
            default:
              return {
                jsonrpc: '2.0',
                id: request.id || null,
                error: {
                  code: -32602,
                  message: `Unknown operation: ${operation}`
                }
              };
          }

          return {
            jsonrpc: '2.0',
            id: request.id || null,
            result: {
              content: [
                {
                  type: 'text',
                  text: `${a} ${operation} ${b} = ${result}`
                }
              ]
            }
          };

        case 'system_info':
          if (!userScopes.includes('admin')) {
            return {
              jsonrpc: '2.0',
              id: request.id || null,
              error: {
                code: -32603,
                message: 'Insufficient permissions: admin scope required'
              }
            };
          }

          return {
            jsonrpc: '2.0',
            id: request.id || null,
            result: {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({
                    server: {
                      name: "MCP OAuth Server",
                      version: "1.0.0",
                      auth_server_url: Config.AUTH_SERVER_URL,
                      mcp_server_url: Config.MCP_SERVER_URL
                    },
                    environment: {
                      node_env: Config.NODE_ENV,
                      is_development: Config.IS_DEVELOPMENT
                    },
                    current_user: user.sub
                  }, null, 2)
                }
              ]
            }
          };

        default:
          return {
            jsonrpc: '2.0',
            id: request.id || null,
            error: {
              code: -32601,
              message: 'Tool not found',
              data: { tool: name }
            }
          };
      }
    } catch (error) {
      return {
        jsonrpc: '2.0',
        id: request.id || null,
        error: {
          code: -32603,
          message: `Tool execution error: ${error}`
        }
      };
    }
  }

  private handleToolsCall(request: MCPRequest, user: AccessToken): MCPResponse {
    const { name, arguments: args } = request.params;
    const userScopes = user.scope.split(' ');

    switch (name) {
      case 'echo':
        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            content: [
              {
                type: 'text',
                text: `Echo: ${args.message}`
              }
            ]
          }
        };

      case 'get_user_info':
        if (!userScopes.includes('read')) {
          return {
            jsonrpc: '2.0',
            id: request.id || null,
            error: {
              code: -32603,
              message: 'Insufficient permissions',
              data: { required_scope: 'read' }
            }
          };
        }

        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  user: user.sub,
                  scopes: userScopes,
                  client: user.aud,
                  expires: new Date(user.exp * 1000).toISOString()
                }, null, 2)
              }
            ]
          }
        };

      default:
        return {
          jsonrpc: '2.0',
          id: request.id || null,
          error: {
            code: -32601,
            message: 'Tool not found',
            data: { tool: name }
          }
        };
    }
  }

  private handleResourcesList(request: MCPRequest, user: AccessToken): MCPResponse {
    return {
      jsonrpc: '2.0',
      id: request.id || null,
      result: {
        resources: [
          {
            uri: 'mcp://example/resource1',
            name: 'Example Resource 1',
            description: 'An example resource',
            mimeType: 'text/plain'
          }
        ]
      }
    };
  }

  private handleResourcesRead(request: MCPRequest, user: AccessToken): MCPResponse {
    const { uri } = request.params;

    if (uri === 'mcp://example/resource1') {
      return {
        jsonrpc: '2.0',
        id: request.id || null,
        result: {
          contents: [
            {
              uri,
              mimeType: 'text/plain',
              text: `Hello from MCP OAuth Server! User: ${user.sub}, Scopes: ${user.scope}`
            }
          ]
        }
      };
    }

    return {
      jsonrpc: '2.0',
      id: request.id || null,
      error: {
        code: -32602,
        message: 'Resource not found',
        data: { uri }
      }
    };
  }

  private async handleResourcesReadSDK(request: MCPRequest, user: AccessToken): Promise<MCPResponse> {
    const { uri } = request.params;
    const userScopes = user.scope.split(' ');

    try {
      if (uri.startsWith('user://')) {
        if (!userScopes.includes('read')) {
          return {
            jsonrpc: '2.0',
            id: request.id || null,
            error: {
              code: -32603,
              message: 'Insufficient permissions: read scope required'
            }
          };
        }

        const username = uri.replace('user://', '');
        const userInfo = UserManager.getUser(username);

        if (!userInfo) {
          return {
            jsonrpc: '2.0',
            id: request.id || null,
            error: {
              code: -32602,
              message: 'User not found',
              data: { uri }
            }
          };
        }

        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            contents: [{
              uri,
              mimeType: 'application/json',
              text: JSON.stringify({
                username: userInfo.username,
                scopes: userInfo.scopes,
                authorized: true
              }, null, 2)
            }]
          }
        };
      }

      if (uri.startsWith('greeting://')) {
        const name = uri.replace('greeting://', '');
        const greeting = `Hello, ${name}! You are authenticated as ${user.sub}.`;

        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            contents: [{
              uri,
              mimeType: 'text/plain',
              text: greeting
            }]
          }
        };
      }

      if (uri === 'status://server') {
        if (!userScopes.includes('admin')) {
          return {
            jsonrpc: '2.0',
            id: request.id || null,
            error: {
              code: -32603,
              message: 'Insufficient permissions: admin scope required'
            }
          };
        }

        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            contents: [{
              uri,
              mimeType: 'application/json',
              text: JSON.stringify({
                status: "healthy",
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                current_user: user.sub
              }, null, 2)
            }]
          }
        };
      }

      return {
        jsonrpc: '2.0',
        id: request.id || null,
        error: {
          code: -32602,
          message: 'Resource not found',
          data: { uri }
        }
      };
    } catch (error) {
      return {
        jsonrpc: '2.0',
        id: request.id || null,
        error: {
          code: -32603,
          message: `Resource read error: ${error}`
        }
      };
    }
  }

  private handlePromptsList(request: MCPRequest, user: AccessToken): MCPResponse {
    return {
      jsonrpc: '2.0',
      id: request.id || null,
      result: {
        prompts: [
          {
            name: 'user_assistant',
            description: 'A helpful assistant for the authenticated user'
          },
          {
            name: 'permission_check',
            description: 'Check what permissions the current user has'
          }
        ]
      }
    };
  }

  private async handlePromptsGet(request: MCPRequest, user: AccessToken): Promise<MCPResponse> {
    const { name } = request.params;

    switch (name) {
      case 'user_assistant':
        const userScopes = user.scope.split(' ');
        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            messages: [
              {
                role: 'system',
                content: {
                  type: 'text',
                  text: `You are a helpful assistant for ${user.sub}. The user has the following permissions: ${userScopes.join(', ')}. Please assist them with tasks within their permission scope.`
                }
              },
              {
                role: 'user',
                content: {
                  type: 'text',
                  text: 'Hello! I need help with my tasks.'
                }
              }
            ]
          }
        };

      case 'permission_check':
        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            messages: [
              {
                role: 'system',
                content: {
                  type: 'text',
                  text: `Current user: ${user.sub}\nPermissions: ${user.scope}\nClient: ${user.aud}\nToken expires: ${new Date(user.exp * 1000).toISOString()}`
                }
              }
            ]
          }
        };

      default:
        return {
          jsonrpc: '2.0',
          id: request.id || null,
          error: {
            code: -32601,
            message: 'Prompt not found',
            data: { prompt: name }
          }
        };
    }
  }

  private handleSSEConnection(req: Request, res: Response): void {
    // SSE 实现（简化版）
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*'
    });

    res.write('data: {"type":"connection","status":"connected"}\n\n');

    // 保持连接活跃
    const keepAlive = setInterval(() => {
      res.write('data: {"type":"ping"}\n\n');
    }, 30000);

    req.on('close', () => {
      clearInterval(keepAlive);
    });
  }

  private handleToolsRequest(req: Request, res: Response): void {
    const user = (req as any).user as AccessToken;
    
    res.json({
      tools: [
        {
          name: 'echo',
          description: 'Echo back the input message'
        },
        {
          name: 'get_user_info',
          description: 'Get current user information'
        }
      ],
      user: user.sub,
      scopes: user.scope.split(' ')
    });
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(Config.MCP_SERVER_PORT, () => {
          console.log(`🔧 MCP Server listening on port ${Config.MCP_SERVER_PORT}`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          if (error.code === 'EADDRINUSE') {
            reject(new Error(`Port ${Config.MCP_SERVER_PORT} is already in use`));
          } else {
            reject(error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🔧 MCP Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}
