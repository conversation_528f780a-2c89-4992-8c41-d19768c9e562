import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { Config } from './config';
import { JWTUtils } from './jwt-utils';
import { OAuthHandler } from './oauth-handler';
import { MCPRequest, MCPResponse, MCPTool, AccessToken } from './types';

export class MCPServer {
  private app: Express;
  private server: any;

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    // 安全中间件
    this.app.use(helmet());

    // CORS 配置
    this.app.use(cors({
      origin: Config.IS_DEVELOPMENT ? true : [Config.AUTH_SERVER_URL],
      credentials: true
    }));

    // 解析 JSON
    this.app.use(express.json());

    // Bearer Token 验证中间件
    this.app.use('/mcp', this.authenticateToken.bind(this));
  }

  private authenticateToken(req: Request, res: Response, next: any): void {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
      }).json({
        error: 'invalid_token',
        error_description: 'Missing access token'
      });
      return;
    }

    try {
      const decoded: AccessToken = JWTUtils.verifyAccessToken(token);
      (req as any).user = decoded;
      next();
    } catch (error) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
      }).json({
        error: 'invalid_token',
        error_description: 'Invalid or expired access token'
      });
    }
  }

  private setupRoutes(): void {
    // 资源服务器元数据端点
    this.app.get('/.well-known/oauth-protected-resource', (req: Request, res: Response) => {
      res.json(OAuthHandler.getResourceServerMetadata());
    });

    // MCP Streamable HTTP 传输端点
    this.app.post('/mcp', (req: Request, res: Response) => {
      this.handleMCPRequest(req, res);
    });

    // SSE 传输端点（兼容性）
    this.app.get('/sse', (req: Request, res: Response) => {
      this.handleSSEConnection(req, res);
    });

    // MCP 工具端点示例
    this.app.get('/mcp/v1/tools', (req: Request, res: Response) => {
      this.handleToolsRequest(req, res);
    });

    // 健康检查端点
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'mcp-server'
      });
    });

    // 错误处理中间件
    this.app.use((err: any, req: Request, res: Response, next: any) => {
      console.error('MCP server error:', err);
      res.status(500).json({
        error: 'server_error',
        error_description: 'Internal server error'
      });
    });

    // 404 处理
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        error: 'not_found',
        error_description: 'Endpoint not found'
      });
    });
  }

  private handleMCPRequest(req: Request, res: Response): void {
    try {
      const mcpRequest: MCPRequest = req.body;
      const user = (req as any).user as AccessToken;

      console.log(`📨 MCP Request from ${user.sub}: ${mcpRequest.method}`);

      // 验证 MCP 协议版本
      const protocolVersion = req.headers['mcp-protocol-version'];
      if (protocolVersion && protocolVersion !== '2024-11-05') {
        res.status(400).json({
          jsonrpc: '2.0',
          id: mcpRequest.id,
          error: {
            code: -32600,
            message: 'Unsupported MCP protocol version'
          }
        });
        return;
      }

      // 处理不同的 MCP 方法
      let response: MCPResponse;

      switch (mcpRequest.method) {
        case 'initialize':
          response = this.handleInitialize(mcpRequest, user);
          break;
        case 'tools/list':
          response = this.handleToolsList(mcpRequest, user);
          break;
        case 'tools/call':
          response = this.handleToolsCall(mcpRequest, user);
          break;
        case 'resources/list':
          response = this.handleResourcesList(mcpRequest, user);
          break;
        case 'resources/read':
          response = this.handleResourcesRead(mcpRequest, user);
          break;
        default:
          response = {
            jsonrpc: '2.0',
            id: mcpRequest.id || null,
            error: {
              code: -32601,
              message: 'Method not found',
              data: { method: mcpRequest.method }
            }
          };
      }

      res.json(response);
    } catch (error) {
      console.error('MCP request error:', error);
      res.status(500).json({
        jsonrpc: '2.0',
        id: req.body.id,
        error: {
          code: -32603,
          message: 'Internal error'
        }
      });
    }
  }

  private handleInitialize(request: MCPRequest, user: AccessToken): MCPResponse {
    return {
      jsonrpc: '2.0',
      id: request.id || null,
      result: {
        protocolVersion: '2024-11-05',
        capabilities: {
          tools: {},
          resources: {},
          prompts: {}
        },
        serverInfo: {
          name: 'MCP OAuth Server',
          version: '1.0.0'
        }
      }
    };
  }

  private handleToolsList(request: MCPRequest, user: AccessToken): MCPResponse {
    const tools: MCPTool[] = [
      {
        name: 'echo',
        description: 'Echo back the input message',
        inputSchema: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'Message to echo back'
            }
          },
          required: ['message']
        }
      },
      {
        name: 'get_user_info',
        description: 'Get current user information',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      }
    ];

    // 根据用户权限过滤工具
    const userScopes = user.scope.split(' ');
    const filteredTools = tools.filter(tool => {
      if (tool.name === 'get_user_info' && !userScopes.includes('read')) {
        return false;
      }
      return true;
    });

    return {
      jsonrpc: '2.0',
      id: request.id || null,
      result: {
        tools: filteredTools
      }
    };
  }

  private handleToolsCall(request: MCPRequest, user: AccessToken): MCPResponse {
    const { name, arguments: args } = request.params;
    const userScopes = user.scope.split(' ');

    switch (name) {
      case 'echo':
        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            content: [
              {
                type: 'text',
                text: `Echo: ${args.message}`
              }
            ]
          }
        };

      case 'get_user_info':
        if (!userScopes.includes('read')) {
          return {
            jsonrpc: '2.0',
            id: request.id || null,
            error: {
              code: -32603,
              message: 'Insufficient permissions',
              data: { required_scope: 'read' }
            }
          };
        }

        return {
          jsonrpc: '2.0',
          id: request.id || null,
          result: {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  user: user.sub,
                  scopes: userScopes,
                  client: user.aud,
                  expires: new Date(user.exp * 1000).toISOString()
                }, null, 2)
              }
            ]
          }
        };

      default:
        return {
          jsonrpc: '2.0',
          id: request.id || null,
          error: {
            code: -32601,
            message: 'Tool not found',
            data: { tool: name }
          }
        };
    }
  }

  private handleResourcesList(request: MCPRequest, user: AccessToken): MCPResponse {
    return {
      jsonrpc: '2.0',
      id: request.id || null,
      result: {
        resources: [
          {
            uri: 'mcp://example/resource1',
            name: 'Example Resource 1',
            description: 'An example resource',
            mimeType: 'text/plain'
          }
        ]
      }
    };
  }

  private handleResourcesRead(request: MCPRequest, user: AccessToken): MCPResponse {
    const { uri } = request.params;

    if (uri === 'mcp://example/resource1') {
      return {
        jsonrpc: '2.0',
        id: request.id || null,
        result: {
          contents: [
            {
              uri,
              mimeType: 'text/plain',
              text: `Hello from MCP OAuth Server! User: ${user.sub}, Scopes: ${user.scope}`
            }
          ]
        }
      };
    }

    return {
      jsonrpc: '2.0',
      id: request.id || null,
      error: {
        code: -32602,
        message: 'Resource not found',
        data: { uri }
      }
    };
  }

  private handleSSEConnection(req: Request, res: Response): void {
    // SSE 实现（简化版）
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*'
    });

    res.write('data: {"type":"connection","status":"connected"}\n\n');

    // 保持连接活跃
    const keepAlive = setInterval(() => {
      res.write('data: {"type":"ping"}\n\n');
    }, 30000);

    req.on('close', () => {
      clearInterval(keepAlive);
    });
  }

  private handleToolsRequest(req: Request, res: Response): void {
    const user = (req as any).user as AccessToken;
    
    res.json({
      tools: [
        {
          name: 'echo',
          description: 'Echo back the input message'
        },
        {
          name: 'get_user_info',
          description: 'Get current user information'
        }
      ],
      user: user.sub,
      scopes: user.scope.split(' ')
    });
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(Config.MCP_SERVER_PORT, () => {
          console.log(`🔧 MCP Server listening on port ${Config.MCP_SERVER_PORT}`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          if (error.code === 'EADDRINUSE') {
            reject(new Error(`Port ${Config.MCP_SERVER_PORT} is already in use`));
          } else {
            reject(error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🔧 MCP Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}
