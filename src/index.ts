import { Config } from './config';
import { UserManager } from './user-manager';
import { AuthServer } from './auth-server';
import { MCPStreamableHTTPServer } from './mcp-streamable-http-server';

async function main() {
  try {
    // 验证配置
    Config.validateConfig();
    console.log('✅ Configuration validated');
    
    // 初始化用户管理器
    UserManager.initialize();
    console.log('✅ User manager initialized');
    
    // 启动授权服务器
    const authServer = new AuthServer();
    await authServer.start();
    console.log(`✅ Auth Server started on port ${Config.AUTH_SERVER_PORT}`);
    
    // 启动 MCP Streamable HTTP 服务器
    const mcpServer = new MCPStreamableHTTPServer();
    await mcpServer.start();
    console.log(`✅ MCP Streamable HTTP Server started on port ${Config.MCP_SERVER_PORT}`);
    
    console.log('\n🚀 MCP OAuth Server is running!');
    console.log(`📋 Auth Server: ${Config.AUTH_SERVER_URL}`);
    console.log(`🔧 MCP Server: ${Config.MCP_SERVER_URL}`);
    console.log(`👥 Authorized users: ${UserManager.getAllUsers().join(', ')}`);
    
    // 优雅关闭处理
    process.on('SIGINT', async () => {
      console.log('\n🛑 Shutting down servers...');
      await authServer.stop();
      await mcpServer.stop();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Failed to start servers:', error);
    process.exit(1);
  }
}

// 启动应用
main().catch(console.error);
