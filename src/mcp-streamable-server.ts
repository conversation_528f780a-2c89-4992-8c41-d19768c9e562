import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { Config } from './config';
import { JWTUtils } from './jwt-utils';
import { <PERSON>AuthHandler } from './oauth-handler';
import { MCPSDKServer } from './mcp-sdk-server';
import { AccessToken } from './types';

export class MCPStreamableServer {
  private app: Express;
  private server: any;
  private mcpSDKServer: MCPSDKServer;

  constructor() {
    this.app = express();
    this.mcpSDKServer = new MCPSDKServer();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private setupMiddleware(): void {
    // 安全中间件 - 在开发环境中禁用 CSP
    if (Config.IS_DEVELOPMENT) {
      this.app.use(helmet({
        contentSecurityPolicy: false
      }));
    } else {
      this.app.use(helmet());
    }

    // CORS 配置 - 允许 MCP Inspector 连接
    this.app.use(cors({
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // 解析 JSON
    this.app.use(express.json());

    // Bearer Token 验证中间件（仅对 /mcp 路径）
    this.app.use('/mcp', this.authenticateToken.bind(this));
  }

  private authenticateToken(req: Request, res: Response, next: any): void {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1]; // Bearer TOKEN

    if (!token) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
      }).json({
        error: 'invalid_token',
        error_description: 'Missing access token'
      });
      return;
    }

    try {
      const decoded: AccessToken = JWTUtils.verifyAccessToken(token);
      (req as any).user = decoded;
      
      // 设置访问令牌到 SDK 服务器
      this.mcpSDKServer.setAccessToken(token);
      next();
    } catch (error) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}/.well-known/oauth-authorization-server"`
      }).json({
        error: 'invalid_token',
        error_description: 'Invalid or expired access token'
      });
    }
  }

  private setupRoutes(): void {
    // 资源服务器元数据端点
    this.app.get('/.well-known/oauth-protected-resource', (req: Request, res: Response) => {
      res.json(OAuthHandler.getResourceServerMetadata());
    });

    // MCP Streamable HTTP 传输端点
    this.app.post('/mcp', (req: Request, res: Response) => {
      this.handleStreamableHTTP(req, res);
    });

    // 健康检查端点
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'mcp-streamable-server'
      });
    });

    // 错误处理中间件
    this.app.use((err: any, req: Request, res: Response, next: any) => {
      console.error('MCP streamable server error:', err);
      res.status(500).json({
        error: 'server_error',
        error_description: 'Internal server error'
      });
    });

    // 404 处理
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        error: 'not_found',
        error_description: 'Endpoint not found'
      });
    });
  }

  private async handleStreamableHTTP(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user as AccessToken;
      console.log(`📨 Streamable HTTP request from ${user.sub}`);

      // 设置 SSE 头部
      res.writeHead(200, {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Allow-Methods': 'POST, OPTIONS'
      });

      // 处理请求体中的消息
      const messages = req.body;
      
      if (!Array.isArray(messages)) {
        // 单个消息
        const response = await this.processMessage(messages, user);
        res.write(JSON.stringify(response) + '\n');
      } else {
        // 多个消息
        for (const message of messages) {
          const response = await this.processMessage(message, user);
          res.write(JSON.stringify(response) + '\n');
        }
      }

      res.end();
    } catch (error) {
      console.error('Streamable HTTP error:', error);
      res.status(500).json({
        error: 'server_error',
        error_description: 'Internal server error'
      });
    }
  }

  private async processMessage(message: any, user: AccessToken): Promise<any> {
    try {
      console.log('Processing message:', JSON.stringify(message, null, 2));

      // 验证消息格式
      if (!message.jsonrpc || message.jsonrpc !== '2.0') {
        return {
          jsonrpc: '2.0',
          id: message.id || null,
          error: {
            code: -32600,
            message: 'Invalid Request',
            data: 'Missing or invalid jsonrpc field'
          }
        };
      }

      // 处理不同的 MCP 方法
      switch (message.method) {
        case 'initialize':
          return {
            jsonrpc: '2.0',
            id: message.id,
            result: {
              protocolVersion: '2024-11-05',
              capabilities: {
                tools: {},
                resources: {},
                prompts: {}
              },
              serverInfo: {
                name: 'MCP OAuth Server',
                version: '1.0.0'
              }
            }
          };

        case 'tools/list':
          return await this.handleToolsList(message, user);

        case 'tools/call':
          return await this.handleToolsCall(message, user);

        case 'resources/list':
          return await this.handleResourcesList(message, user);

        case 'resources/read':
          return await this.handleResourcesRead(message, user);

        case 'prompts/list':
          return await this.handlePromptsList(message, user);

        case 'prompts/get':
          return await this.handlePromptsGet(message, user);

        default:
          return {
            jsonrpc: '2.0',
            id: message.id || null,
            error: {
              code: -32601,
              message: 'Method not found',
              data: { method: message.method }
            }
          };
      }
    } catch (error) {
      console.error('Message processing error:', error);
      return {
        jsonrpc: '2.0',
        id: message.id || null,
        error: {
          code: -32603,
          message: 'Internal error',
          data: String(error)
        }
      };
    }
  }

  private async handleToolsList(message: any, user: AccessToken): Promise<any> {
    const userScopes = user.scope.split(' ');
    const tools: any[] = [
      {
        name: 'echo',
        description: 'Echo back the input message',
        inputSchema: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'Message to echo back'
            }
          },
          required: ['message']
        }
      }
    ];

    // 根据权限添加工具
    if (userScopes.includes('read')) {
      tools.push({
        name: 'get_user_info',
        description: 'Get current user information',
        inputSchema: {
          type: 'object',
          properties: {},
          required: []
        }
      });
    }

    if (userScopes.includes('execute')) {
      tools.push({
        name: 'calculator',
        description: 'Perform basic arithmetic operations',
        inputSchema: {
          type: 'object',
          properties: {
            operation: {
              type: 'string',
              enum: ['add', 'subtract', 'multiply', 'divide'],
              description: 'The arithmetic operation to perform'
            },
            a: {
              type: 'number',
              description: 'First number'
            },
            b: {
              type: 'number',
              description: 'Second number'
            }
          },
          required: ['operation', 'a', 'b']
        }
      });
    }

    if (userScopes.includes('admin')) {
      tools.push({
        name: 'system_info',
        description: 'Get system information',
        inputSchema: {
          type: 'object',
          properties: {},
          required: []
        }
      });
    }

    return {
      jsonrpc: '2.0',
      id: message.id,
      result: {
        tools: tools
      }
    };
  }

  private async handleToolsCall(message: any, user: AccessToken): Promise<any> {
    const { name, arguments: args } = message.params;
    const userScopes = user.scope.split(' ');

    switch (name) {
      case 'echo':
        return {
          jsonrpc: '2.0',
          id: message.id,
          result: {
            content: [
              {
                type: 'text',
                text: `Echo: ${args.message}`
              }
            ]
          }
        };

      case 'get_user_info':
        if (!userScopes.includes('read')) {
          return {
            jsonrpc: '2.0',
            id: message.id,
            error: {
              code: -32603,
              message: 'Insufficient permissions: read scope required'
            }
          };
        }

        return {
          jsonrpc: '2.0',
          id: message.id,
          result: {
            content: [
              {
                type: 'text',
                text: JSON.stringify({
                  user: user.sub,
                  scopes: userScopes,
                  client: user.aud,
                  expires: new Date(user.exp * 1000).toISOString()
                }, null, 2)
              }
            ]
          }
        };

      case 'calculator':
        if (!userScopes.includes('execute')) {
          return {
            jsonrpc: '2.0',
            id: message.id,
            error: {
              code: -32603,
              message: 'Insufficient permissions: execute scope required'
            }
          };
        }

        const { operation, a, b } = args;
        let result: number;
        
        switch (operation) {
          case 'add':
            result = a + b;
            break;
          case 'subtract':
            result = a - b;
            break;
          case 'multiply':
            result = a * b;
            break;
          case 'divide':
            if (b === 0) {
              return {
                jsonrpc: '2.0',
                id: message.id,
                error: {
                  code: -32602,
                  message: 'Division by zero is not allowed'
                }
              };
            }
            result = a / b;
            break;
          default:
            return {
              jsonrpc: '2.0',
              id: message.id,
              error: {
                code: -32602,
                message: `Unknown operation: ${operation}`
              }
            };
        }

        return {
          jsonrpc: '2.0',
          id: message.id,
          result: {
            content: [
              {
                type: 'text',
                text: `${a} ${operation} ${b} = ${result}`
              }
            ]
          }
        };

      default:
        return {
          jsonrpc: '2.0',
          id: message.id,
          error: {
            code: -32601,
            message: 'Tool not found',
            data: { tool: name }
          }
        };
    }
  }

  private async handleResourcesList(message: any, user: AccessToken): Promise<any> {
    return {
      jsonrpc: '2.0',
      id: message.id,
      result: {
        resources: [
          {
            uri: 'greeting://world',
            name: 'Greeting Resource',
            description: 'A simple greeting resource',
            mimeType: 'text/plain'
          }
        ]
      }
    };
  }

  private async handleResourcesRead(message: any, user: AccessToken): Promise<any> {
    const { uri } = message.params;

    if (uri.startsWith('greeting://')) {
      const name = uri.replace('greeting://', '');
      return {
        jsonrpc: '2.0',
        id: message.id,
        result: {
          contents: [
            {
              uri,
              mimeType: 'text/plain',
              text: `Hello, ${name}! You are authenticated as ${user.sub}.`
            }
          ]
        }
      };
    }

    return {
      jsonrpc: '2.0',
      id: message.id,
      error: {
        code: -32602,
        message: 'Resource not found',
        data: { uri }
      }
    };
  }

  private async handlePromptsList(message: any, user: AccessToken): Promise<any> {
    return {
      jsonrpc: '2.0',
      id: message.id,
      result: {
        prompts: [
          {
            name: 'user_assistant',
            description: 'A helpful assistant for the authenticated user'
          }
        ]
      }
    };
  }

  private async handlePromptsGet(message: any, user: AccessToken): Promise<any> {
    const { name } = message.params;

    if (name === 'user_assistant') {
      return {
        jsonrpc: '2.0',
        id: message.id,
        result: {
          messages: [
            {
              role: 'user',
              content: {
                type: 'text',
                text: `You are a helpful assistant for ${user.sub}. The user has permissions: ${user.scope}.`
              }
            }
          ]
        }
      };
    }

    return {
      jsonrpc: '2.0',
      id: message.id,
      error: {
        code: -32601,
        message: 'Prompt not found',
        data: { prompt: name }
      }
    };
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(Config.MCP_SERVER_PORT, () => {
          console.log(`🔧 MCP Streamable Server listening on port ${Config.MCP_SERVER_PORT}`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          if (error.code === 'EADDRINUSE') {
            reject(new Error(`Port ${Config.MCP_SERVER_PORT} is already in use`));
          } else {
            reject(error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🔧 MCP Streamable Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}
