import { Request, Response, NextFunction } from 'express';
import { JWTUtils } from './jwt-utils';
import { Config } from './config';
import { AccessToken } from './types';

// 扩展 Request 类型以包含认证信息
declare global {
  namespace Express {
    interface Request {
      auth?: {
        token: string;
        user: AccessToken;
        clientId: string;
        scopes: string[];
        expiresAt: number;
      };
    }
  }
}

export interface OAuthMiddlewareOptions {
  requiredScopes?: string[];
  resourceMetadataUrl?: string;
}

export function requireBearerAuth(options: OAuthMiddlewareOptions = {}) {
  return (req: Request, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      // 返回 OAuth 挑战
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}"`
      }).json({
        jsonrpc: '2.0',
        error: {
          code: -32001,
          message: 'Authentication required',
          data: {
            auth_server: Config.AUTH_SERVER_URL,
            scopes: Config.SUPPORTED_SCOPES
          }
        },
        id: null
      });
      return;
    }

    try {
      const user: AccessToken = JWTUtils.verifyAccessToken(token);
      
      // 检查所需权限
      if (options.requiredScopes && options.requiredScopes.length > 0) {
        const userScopes = user.scope.split(' ');
        const hasRequiredScopes = options.requiredScopes.every(scope => 
          userScopes.includes(scope)
        );
        
        if (!hasRequiredScopes) {
          res.status(403).json({
            jsonrpc: '2.0',
            error: {
              code: -32002,
              message: 'Insufficient permissions',
              data: {
                required_scopes: options.requiredScopes,
                user_scopes: userScopes
              }
            },
            id: null
          });
          return;
        }
      }

      // 添加认证信息到请求对象
      req.auth = {
        token,
        user,
        clientId: user.aud,
        scopes: user.scope.split(' '),
        expiresAt: user.exp
      };

      next();
    } catch (error) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}"`
      }).json({
        jsonrpc: '2.0',
        error: {
          code: -32001,
          message: 'Invalid or expired access token',
          data: {
            auth_server: Config.AUTH_SERVER_URL,
            scopes: Config.SUPPORTED_SCOPES
          }
        },
        id: null
      });
    }
  };
}

export function optionalBearerAuth(options: OAuthMiddlewareOptions = {}) {
  return (req: Request, res: Response, next: NextFunction) => {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      // 没有令牌，继续处理但不设置 auth
      next();
      return;
    }

    try {
      const user: AccessToken = JWTUtils.verifyAccessToken(token);
      
      // 添加认证信息到请求对象
      req.auth = {
        token,
        user,
        clientId: user.aud,
        scopes: user.scope.split(' '),
        expiresAt: user.exp
      };

      next();
    } catch (error) {
      // 令牌无效，但不阻止请求
      console.warn('Invalid token provided:', error);
      next();
    }
  };
}
