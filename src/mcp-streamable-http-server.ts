import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { McpServer, ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';
import { Config } from './config';
import { JWTUtils } from './jwt-utils';
import { OAuthHandler } from './oauth-handler';
import { UserManager } from './user-manager';
import { AccessToken } from './types';

interface SessionInfo {
  user: AccessToken;
  mcpServer: McpServer;
  lastActivity: number;
  eventCounter: number;
  sseConnection?: Response;
}

export class MCPStreamableHTTPServer {
  private app: Express;
  private server: any;
  private sessions: Map<string, SessionInfo> = new Map();

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
  }

  private createMCPServer(user: AccessToken): McpServer {
    const server = new McpServer({
      name: 'MCP OAuth Server',
      version: '1.0.0'
    });

    // Echo 工具
    server.registerTool(
      'echo',
      {
        title: 'Echo Tool',
        description: 'Echo back the input message',
        inputSchema: {
          message: z.string().describe('Message to echo back')
        }
      },
      async ({ message }) => {
        return {
          content: [
            {
              type: 'text',
              text: `Echo: ${message}`
            }
          ]
        };
      }
    );

    // 用户信息工具
    server.registerTool(
      'get_user_info',
      {
        title: 'Get User Info',
        description: 'Get current authenticated user information',
        inputSchema: {}
      },
      async () => {
        if (!this.hasScope(user, 'read')) {
          throw new Error('Insufficient permissions: read scope required');
        }

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                user: user.sub,
                scopes: user.scope.split(' '),
                client: user.aud,
                expires: new Date(user.exp * 1000).toISOString()
              }, null, 2)
            }
          ]
        };
      }
    );

    // 计算器工具
    server.registerTool(
      'calculator',
      {
        title: 'Calculator',
        description: 'Perform basic arithmetic operations',
        inputSchema: {
          operation: z.enum(['add', 'subtract', 'multiply', 'divide']).describe('The arithmetic operation to perform'),
          a: z.number().describe('First number'),
          b: z.number().describe('Second number')
        }
      },
      async ({ operation, a, b }) => {
        if (!this.hasScope(user, 'execute')) {
          throw new Error('Insufficient permissions: execute scope required');
        }

        let result: number;
        switch (operation) {
          case 'add':
            result = a + b;
            break;
          case 'subtract':
            result = a - b;
            break;
          case 'multiply':
            result = a * b;
            break;
          case 'divide':
            if (b === 0) {
              throw new Error('Division by zero is not allowed');
            }
            result = a / b;
            break;
          default:
            throw new Error(`Unknown operation: ${operation}`);
        }

        return {
          content: [
            {
              type: 'text',
              text: `${a} ${operation} ${b} = ${result}`
            }
          ]
        };
      }
    );

    // 系统信息工具
    server.registerTool(
      'system_info',
      {
        title: 'System Info',
        description: 'Get system and server information (admin only)',
        inputSchema: {}
      },
      async () => {
        if (!this.hasScope(user, 'admin')) {
          throw new Error('Insufficient permissions: admin scope required');
        }

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                server: {
                  name: 'MCP OAuth Server',
                  version: '1.0.0',
                  auth_server_url: Config.AUTH_SERVER_URL,
                  mcp_server_url: Config.MCP_SERVER_URL
                },
                users: {
                  total: UserManager.getAllUsers().length,
                  authorized_users: UserManager.getAllUsers()
                },
                scopes: {
                  supported: Config.SUPPORTED_SCOPES
                },
                environment: {
                  node_env: Config.NODE_ENV,
                  is_development: Config.IS_DEVELOPMENT
                }
              }, null, 2)
            }
          ]
        };
      }
    );

    // 资源
    server.registerResource(
      'greeting',
      new ResourceTemplate('greeting://{name}', { list: undefined }),
      {
        title: 'Greeting Resource',
        description: 'A simple greeting resource'
      },
      async (uri, { name }) => {
        return {
          contents: [{
            uri: uri.href,
            mimeType: 'text/plain',
            text: `Hello, ${name}! You are authenticated as ${user.sub}.`
          }]
        };
      }
    );

    // 提示
    server.registerPrompt(
      'user_assistant',
      {
        title: 'User Assistant',
        description: 'A helpful assistant for the authenticated user',
        argsSchema: {}
      },
      () => {
        return {
          messages: [
            {
              role: 'user',
              content: {
                type: 'text',
                text: `You are a helpful assistant for ${user.sub}. The user has permissions: ${user.scope}.`
              }
            }
          ]
        };
      }
    );

    return server;
  }

  private hasScope(user: AccessToken, scope: string): boolean {
    const userScopes = user.scope.split(' ');
    return userScopes.includes(scope);
  }

  private setupMiddleware(): void {
    // 安全中间件
    if (Config.IS_DEVELOPMENT) {
      this.app.use(helmet({
        contentSecurityPolicy: false
      }));
    } else {
      this.app.use(helmet());
    }

    // CORS 配置
    this.app.use(cors({
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'OPTIONS', 'DELETE'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Mcp-Session-Id', 'Last-Event-ID', 'Accept'],
      exposedHeaders: ['Mcp-Session-Id']
    }));

    // 解析 JSON
    this.app.use(express.json());

    // 添加请求日志中间件
    this.app.use((req, res, next) => {
      console.log(`📥 ${req.method} ${req.path} - User: ${(req as any).user?.sub || 'anonymous'}`);
      next();
    });
  }

  private authenticateToken(req: Request, res: Response, next: any): void {
    const authHeader = req.headers.authorization;
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="OAuth", error="invalid_token", error_description="Missing or invalid access token"`
      }).json({
        error: 'invalid_token',
        error_description: 'Missing or invalid access token'
      });
      return;
    }

    try {
      const decoded: AccessToken = JWTUtils.verifyAccessToken(token);
      (req as any).user = decoded;
      next();
    } catch (error) {
      res.status(401).set({
        'WWW-Authenticate': `Bearer realm="OAuth", error="invalid_token", error_description="Invalid or expired access token"`
      }).json({
        error: 'invalid_token',
        error_description: 'Invalid or expired access token'
      });
    }
  }

  private setupRoutes(): void {
    // 资源服务器元数据端点
    this.app.get('/.well-known/oauth-protected-resource', (req: Request, res: Response) => {
      res.json(OAuthHandler.getResourceServerMetadata());
    });

    // MCP Streamable HTTP 端点 (主要端点)
    this.app.post('/mcp', this.authenticateToken.bind(this), async (req: Request, res: Response) => {
      await this.handleStreamableHTTPRequest(req, res);
    });

    this.app.get('/mcp', this.authenticateToken.bind(this), async (req: Request, res: Response) => {
      await this.handleStreamableHTTPSSE(req, res);
    });

    this.app.delete('/mcp', this.authenticateToken.bind(this), async (req: Request, res: Response) => {
      await this.handleSessionTermination(req, res);
    });

    // Legacy SSE 端点 (向后兼容，已弃用)
    this.app.get('/sse', this.authenticateToken.bind(this), async (req: Request, res: Response) => {
      await this.handleLegacySSE(req, res);
    });

    // MCP Inspector 期望的 SSE 端点
    this.app.post('/sse', this.authenticateToken.bind(this), async (req: Request, res: Response) => {
      await this.handleLegacySSEMessage(req, res);
    });

    this.app.post('/messages', this.authenticateToken.bind(this), async (req: Request, res: Response) => {
      await this.handleLegacySSEMessage(req, res);
    });

    // 健康检查端点
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'mcp-streamable-http-server',
        endpoints: {
          streamable_http: '/mcp',
          legacy_sse: '/sse',
          legacy_messages: '/messages'
        }
      });
    });

    // 错误处理中间件
    this.app.use((err: any, req: Request, res: Response, next: any) => {
      console.error('MCP streamable HTTP server error:', err);
      res.status(500).json({
        error: 'server_error',
        error_description: 'Internal server error'
      });
    });

    // 404 处理
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        error: 'not_found',
        error_description: 'Endpoint not found'
      });
    });
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(Config.MCP_SERVER_PORT, () => {
          console.log(`🔧 MCP Streamable HTTP Server listening on port ${Config.MCP_SERVER_PORT}`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          if (error.code === 'EADDRINUSE') {
            reject(new Error(`Port ${Config.MCP_SERVER_PORT} is already in use`));
          } else {
            reject(error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🔧 MCP Streamable HTTP Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  private async handleStreamableHTTPRequest(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user as AccessToken;
      const sessionId = req.headers['mcp-session-id'] as string;

      console.log(`📨 Streamable HTTP POST from ${user.sub}: ${req.body?.method || 'unknown'}`);

      // 验证 Origin 头部以防止 DNS rebinding 攻击
      this.validateOrigin(req);

      // 处理 JSON-RPC 请求
      const request = req.body;

      // 验证 JSON-RPC 格式
      if (!request.jsonrpc || request.jsonrpc !== '2.0') {
        res.status(400).json({
          jsonrpc: '2.0',
          id: request.id || null,
          error: {
            code: -32600,
            message: 'Invalid Request',
            data: 'Missing or invalid jsonrpc field'
          }
        });
        return;
      }

      // 处理初始化请求
      if (request.method === 'initialize') {
        const newSessionId = this.generateSessionId();
        const mcpServer = this.createMCPServer(user);

        const response = {
          jsonrpc: '2.0',
          id: request.id,
          result: {
            protocolVersion: '2024-11-05',
            capabilities: {
              tools: {},
              resources: {},
              prompts: {}
            },
            serverInfo: {
              name: 'MCP OAuth Server',
              version: '1.0.0'
            }
          }
        };

        // 设置会话 ID
        res.setHeader('Mcp-Session-Id', newSessionId);

        // 存储会话信息
        this.sessions.set(newSessionId, {
          user,
          mcpServer,
          lastActivity: Date.now(),
          eventCounter: 0
        });

        res.json(response);
        return;
      }

      // 获取或创建 MCP 服务器实例
      let mcpServer: McpServer;
      if (sessionId && this.sessions.has(sessionId)) {
        const session = this.sessions.get(sessionId)!;
        session.lastActivity = Date.now();
        mcpServer = session.mcpServer;
      } else {
        mcpServer = this.createMCPServer(user);
      }

      // 处理其他请求
      const response = await this.processMCPRequest(request, mcpServer, user);

      // 检查是否需要流式响应
      const acceptHeader = req.headers.accept || '';
      const needsStreaming = acceptHeader.includes('text/event-stream') && this.shouldUseStreaming(request);

      if (needsStreaming) {
        // 返回 SSE 流
        res.setHeader('Content-Type', 'text/event-stream');
        res.setHeader('Cache-Control', 'no-cache');
        res.setHeader('Connection', 'keep-alive');
        res.setHeader('Access-Control-Allow-Origin', this.getAllowedOrigin(req));
        res.setHeader('Access-Control-Allow-Credentials', 'true');

        // 发送响应作为 SSE 事件
        const eventId = this.generateEventId();
        res.write(`id: ${eventId}\n`);
        res.write(`event: response\n`);
        res.write(`data: ${JSON.stringify(response)}\n\n`);

      } else {
        // 返回单个 JSON 响应
        res.json(response);
      }

    } catch (error) {
      console.error('Streamable HTTP request error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          jsonrpc: '2.0',
          id: null,
          error: {
            code: -32603,
            message: 'Internal error',
            data: String(error)
          }
        });
      }
    }
  }

  private async handleStreamableHTTPSSE(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user as AccessToken;
      const sessionId = req.headers['mcp-session-id'] as string;

      console.log(`📡 Streamable HTTP SSE from ${user.sub}`);

      // 验证 Origin 头部
      this.validateOrigin(req);

      // 设置 SSE 头部
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', this.getAllowedOrigin(req));
      res.setHeader('Access-Control-Allow-Credentials', 'true');

      // 发送连接确认
      res.write(`id: ${this.generateEventId()}\n`);
      res.write(`event: connected\n`);
      res.write(`data: {"status": "connected", "timestamp": "${new Date().toISOString()}"}\n\n`);

      // 如果有会话 ID，可以发送服务器端的通知/请求
      if (sessionId && this.sessions.has(sessionId)) {
        const session = this.sessions.get(sessionId)!;
        session.sseConnection = res;
      }

      // 保持连接活跃
      const keepAlive = setInterval(() => {
        if (!res.destroyed) {
          res.write(`id: ${this.generateEventId()}\n`);
          res.write(`event: ping\n`);
          res.write(`data: {"timestamp": "${new Date().toISOString()}"}\n\n`);
        } else {
          clearInterval(keepAlive);
        }
      }, 30000);

      // 清理连接
      req.on('close', () => {
        clearInterval(keepAlive);
        if (sessionId && this.sessions.has(sessionId)) {
          const session = this.sessions.get(sessionId)!;
          if (session.sseConnection === res) {
            delete session.sseConnection;
          }
        }
      });

    } catch (error) {
      console.error('Streamable HTTP SSE error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          error: 'server_error',
          error_description: 'Failed to establish SSE connection'
        });
      }
    }
  }

  private async handleSessionTermination(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.headers['mcp-session-id'] as string;

      if (!sessionId) {
        res.status(400).json({
          error: 'missing_session_id',
          error_description: 'Mcp-Session-Id header is required'
        });
        return;
      }

      if (this.sessions.has(sessionId)) {
        const session = this.sessions.get(sessionId)!;

        // 关闭 SSE 连接
        if (session.sseConnection && !session.sseConnection.destroyed) {
          session.sseConnection.end();
        }

        // 清理会话
        this.sessions.delete(sessionId);

        console.log(`🗑️ Session ${sessionId} terminated`);
        res.status(204).send();
      } else {
        res.status(404).json({
          error: 'session_not_found',
          error_description: 'Session not found'
        });
      }
    } catch (error) {
      console.error('Session termination error:', error);
      res.status(500).json({
        error: 'server_error',
        error_description: 'Failed to terminate session'
      });
    }
  }

  private async handleLegacySSE(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user as AccessToken;

      console.log(`📡 Legacy SSE from ${user.sub || 'anonymous'}`);

      // 验证 Origin 头部
      this.validateOrigin(req);

      // 设置 SSE 头部
      res.setHeader('Content-Type', 'text/event-stream');
      res.setHeader('Cache-Control', 'no-cache');
      res.setHeader('Connection', 'keep-alive');
      res.setHeader('Access-Control-Allow-Origin', this.getAllowedOrigin(req));
      res.setHeader('Access-Control-Allow-Credentials', 'true');

      // MCP Inspector 期望的端点信息
      res.write(`event: endpoint\n`);
      res.write(`data: {"uri": "/sse"}\n\n`);

      // 保持连接活跃
      const keepAlive = setInterval(() => {
        if (!res.destroyed) {
          res.write(`event: ping\n`);
          res.write(`data: {"timestamp": "${new Date().toISOString()}"}\n\n`);
        } else {
          clearInterval(keepAlive);
        }
      }, 30000);

      // 清理连接
      req.on('close', () => {
        clearInterval(keepAlive);
      });

    } catch (error) {
      console.error('Legacy SSE error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          error: 'server_error',
          error_description: 'Failed to establish legacy SSE connection'
        });
      }
    }
  }

  private async handleLegacySSEMessage(req: Request, res: Response): Promise<void> {
    try {
      const user = (req as any).user as AccessToken;

      console.log(`📨 Legacy SSE Message from ${user.sub}: ${req.body?.method || 'unknown'}`);

      // 验证 Origin 头部
      this.validateOrigin(req);

      // 创建 MCP 服务器实例
      const mcpServer = this.createMCPServer(user);

      // 处理请求
      const response = await this.processMCPRequest(req.body, mcpServer, user);

      // 返回 JSON 响应
      res.json(response);

    } catch (error) {
      console.error('Legacy SSE message error:', error);
      res.status(500).json({
        jsonrpc: '2.0',
        id: null,
        error: {
          code: -32603,
          message: 'Internal error',
          data: String(error)
        }
      });
    }
  }

  private async processMCPRequest(request: any, mcpServer: McpServer, user: AccessToken): Promise<any> {
    const { method, params, id } = request;

    try {
      switch (method) {
        case 'tools/list':
          const tools = this.getToolsList(user);
          return {
            jsonrpc: '2.0',
            id,
            result: { tools }
          };

        case 'tools/call':
          const { name, arguments: args } = params;
          const toolResult = await this.callTool(name, args, user);
          return {
            jsonrpc: '2.0',
            id,
            result: toolResult
          };

        case 'resources/list':
          const resources = this.getResourcesList(user);
          return {
            jsonrpc: '2.0',
            id,
            result: { resources }
          };

        case 'resources/read':
          const { uri } = params;
          const resourceResult = await this.readResource(uri, user);
          return {
            jsonrpc: '2.0',
            id,
            result: resourceResult
          };

        case 'prompts/list':
          const prompts = this.getPromptsList(user);
          return {
            jsonrpc: '2.0',
            id,
            result: { prompts }
          };

        case 'prompts/get':
          const { name: promptName, arguments: promptArgs } = params;
          const promptResult = await this.getPrompt(promptName, promptArgs || {}, user);
          return {
            jsonrpc: '2.0',
            id,
            result: promptResult
          };

        default:
          return {
            jsonrpc: '2.0',
            id,
            error: {
              code: -32601,
              message: 'Method not found',
              data: { method }
            }
          };
      }
    } catch (error) {
      return {
        jsonrpc: '2.0',
        id,
        error: {
          code: -32603,
          message: String(error)
        }
      };
    }
  }

  private validateOrigin(req: Request): void {
    const origin = req.headers.origin;

    // 在开发环境中允许 localhost
    if (Config.IS_DEVELOPMENT) {
      if (origin && !origin.match(/^https?:\/\/(localhost|127\.0\.0\.1|::1)(:\d+)?$/)) {
        throw new Error('Invalid origin for development environment');
      }
    } else {
      // 在生产环境中实施更严格的 origin 验证
      const allowedOrigins = process.env.ALLOWED_ORIGINS?.split(',') || [];
      if (origin && !allowedOrigins.includes(origin)) {
        throw new Error('Origin not allowed');
      }
    }
  }

  private getAllowedOrigin(req: Request): string {
    const origin = req.headers.origin;

    if (Config.IS_DEVELOPMENT && origin && origin.match(/^https?:\/\/(localhost|127\.0\.0\.1|::1)(:\d+)?$/)) {
      return origin;
    }

    return 'http://localhost:3002'; // 默认值
  }

  private shouldUseStreaming(request: any): boolean {
    // 决定是否使用流式响应的逻辑
    // 例如，对于长时间运行的操作或需要实时更新的情况
    return false; // 简化实现，默认不使用流式响应
  }

  private generateEventId(): string {
    return `${Date.now()}-${Math.random().toString(36).substring(2)}`;
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  private getToolsList(user: AccessToken): any[] {
    const userScopes = user.scope.split(' ');
    const tools: any[] = [
      {
        name: 'echo',
        description: 'Echo back the input message',
        inputSchema: {
          type: 'object',
          properties: {
            message: {
              type: 'string',
              description: 'Message to echo back'
            }
          },
          required: ['message']
        }
      }
    ];

    // 根据权限添加工具
    if (userScopes.includes('read')) {
      tools.push({
        name: 'get_user_info',
        description: 'Get current authenticated user information',
        inputSchema: {
          type: 'object',
          properties: {},
          required: []
        }
      });
    }

    if (userScopes.includes('execute')) {
      tools.push({
        name: 'calculator',
        description: 'Perform basic arithmetic operations',
        inputSchema: {
          type: 'object',
          properties: {
            operation: {
              type: 'string',
              enum: ['add', 'subtract', 'multiply', 'divide'],
              description: 'The arithmetic operation to perform'
            },
            a: {
              type: 'number',
              description: 'First number'
            },
            b: {
              type: 'number',
              description: 'Second number'
            }
          },
          required: ['operation', 'a', 'b']
        }
      });
    }

    if (userScopes.includes('admin')) {
      tools.push({
        name: 'system_info',
        description: 'Get system and server information (admin only)',
        inputSchema: {
          type: 'object',
          properties: {},
          required: []
        }
      });
    }

    return tools;
  }

  private async callTool(name: string, args: any, user: AccessToken): Promise<any> {
    const userScopes = user.scope.split(' ');

    switch (name) {
      case 'echo':
        return {
          content: [
            {
              type: 'text',
              text: `Echo: ${args.message}`
            }
          ]
        };

      case 'get_user_info':
        if (!userScopes.includes('read')) {
          throw new Error('Insufficient permissions: read scope required');
        }

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                user: user.sub,
                scopes: userScopes,
                client: user.aud,
                expires: new Date(user.exp * 1000).toISOString()
              }, null, 2)
            }
          ]
        };

      case 'calculator':
        if (!userScopes.includes('execute')) {
          throw new Error('Insufficient permissions: execute scope required');
        }

        const { operation, a, b } = args;
        let result: number;

        switch (operation) {
          case 'add':
            result = a + b;
            break;
          case 'subtract':
            result = a - b;
            break;
          case 'multiply':
            result = a * b;
            break;
          case 'divide':
            if (b === 0) {
              throw new Error('Division by zero is not allowed');
            }
            result = a / b;
            break;
          default:
            throw new Error(`Unknown operation: ${operation}`);
        }

        return {
          content: [
            {
              type: 'text',
              text: `${a} ${operation} ${b} = ${result}`
            }
          ]
        };

      case 'system_info':
        if (!userScopes.includes('admin')) {
          throw new Error('Insufficient permissions: admin scope required');
        }

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                server: {
                  name: 'MCP OAuth Server',
                  version: '1.0.0',
                  auth_server_url: Config.AUTH_SERVER_URL,
                  mcp_server_url: Config.MCP_SERVER_URL
                },
                users: {
                  total: UserManager.getAllUsers().length,
                  authorized_users: UserManager.getAllUsers()
                },
                scopes: {
                  supported: Config.SUPPORTED_SCOPES
                },
                environment: {
                  node_env: Config.NODE_ENV,
                  is_development: Config.IS_DEVELOPMENT
                }
              }, null, 2)
            }
          ]
        };

      default:
        throw new Error(`Unknown tool: ${name}`);
    }
  }

  private getResourcesList(user: AccessToken): any[] {
    return [
      {
        uri: 'greeting://{name}',
        name: 'Greeting Resource',
        description: 'A simple greeting resource',
        mimeType: 'text/plain'
      },
      {
        uri: 'user://info',
        name: 'User Information',
        description: 'Current user information resource',
        mimeType: 'application/json'
      }
    ];
  }

  private async readResource(uri: string, user: AccessToken): Promise<any> {
    if (uri.startsWith('greeting://')) {
      const name = uri.replace('greeting://', '');
      return {
        contents: [{
          uri,
          mimeType: 'text/plain',
          text: `Hello, ${name}! You are authenticated as ${user.sub}.`
        }]
      };
    }

    if (uri === 'user://info') {
      return {
        contents: [{
          uri,
          mimeType: 'application/json',
          text: JSON.stringify({
            user: user.sub,
            scopes: user.scope.split(' '),
            client: user.aud,
            expires: new Date(user.exp * 1000).toISOString(),
            timestamp: new Date().toISOString()
          }, null, 2)
        }]
      };
    }

    throw new Error(`Resource not found: ${uri}`);
  }

  private getPromptsList(user: AccessToken): any[] {
    return [
      {
        name: 'user_assistant',
        description: 'A helpful assistant for the authenticated user'
      },
      {
        name: 'permission_check',
        description: 'Check user permissions and capabilities'
      }
    ];
  }

  private async getPrompt(name: string, args: any, user: AccessToken): Promise<any> {
    switch (name) {
      case 'user_assistant':
        return {
          messages: [
            {
              role: 'user',
              content: {
                type: 'text',
                text: `You are a helpful assistant for ${user.sub}. The user has permissions: ${user.scope}.`
              }
            }
          ]
        };

      case 'permission_check':
        return {
          messages: [
            {
              role: 'user',
              content: {
                type: 'text',
                text: `Please check what permissions and capabilities are available to the current user: ${user.sub}. Available scopes: ${user.scope}`
              }
            }
          ]
        };

      default:
        throw new Error(`Unknown prompt: ${name}`);
    }
  }
}
