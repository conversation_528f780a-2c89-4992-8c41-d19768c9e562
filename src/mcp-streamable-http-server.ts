import express, { Express, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import { McpServer, ResourceTemplate } from '@modelcontextprotocol/sdk/server/mcp.js';
import { StreamableHTTPServerTransport } from '@modelcontextprotocol/sdk/server/streamableHttp.js';
import { z } from 'zod';
import { Config } from './config';
import { JWTUtils } from './jwt-utils';
import { OAuthHandler } from './oauth-handler';
import { UserManager } from './user-manager';
import { AccessToken } from './types';

export class MCPStreamableHTTPServer {
  private app: Express;
  private server: any;
  private transports: Map<string, StreamableHTTPServerTransport> = new Map();
  private authenticatedSessions: Map<string, { user: AccessToken; lastActivity: number }> = new Map();

  constructor() {
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.startSessionCleanup();
  }

  private createMCPServer(user: AccessToken): McpServer {
    const server = new McpServer({
      name: 'MCP OAuth Server',
      version: '1.0.0'
    });

    // Echo 工具
    server.registerTool(
      'echo',
      {
        title: 'Echo Tool',
        description: 'Echo back the input message',
        inputSchema: {
          message: z.string().describe('Message to echo back')
        }
      },
      async ({ message }) => {
        return {
          content: [
            {
              type: 'text',
              text: `Echo: ${message}`
            }
          ]
        };
      }
    );

    // 用户信息工具
    server.registerTool(
      'get_user_info',
      {
        title: 'Get User Info',
        description: 'Get current authenticated user information',
        inputSchema: {}
      },
      async () => {
        if (!this.hasScope(user, 'read')) {
          throw new Error('Insufficient permissions: read scope required');
        }

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                user: user.sub,
                scopes: user.scope.split(' '),
                client: user.aud,
                expires: new Date(user.exp * 1000).toISOString()
              }, null, 2)
            }
          ]
        };
      }
    );

    // 计算器工具
    server.registerTool(
      'calculator',
      {
        title: 'Calculator',
        description: 'Perform basic arithmetic operations',
        inputSchema: {
          operation: z.enum(['add', 'subtract', 'multiply', 'divide']).describe('The arithmetic operation to perform'),
          a: z.number().describe('First number'),
          b: z.number().describe('Second number')
        }
      },
      async ({ operation, a, b }) => {
        if (!this.hasScope(user, 'execute')) {
          throw new Error('Insufficient permissions: execute scope required');
        }

        let result: number;
        switch (operation) {
          case 'add':
            result = a + b;
            break;
          case 'subtract':
            result = a - b;
            break;
          case 'multiply':
            result = a * b;
            break;
          case 'divide':
            if (b === 0) {
              throw new Error('Division by zero is not allowed');
            }
            result = a / b;
            break;
          default:
            throw new Error(`Unknown operation: ${operation}`);
        }

        return {
          content: [
            {
              type: 'text',
              text: `${a} ${operation} ${b} = ${result}`
            }
          ]
        };
      }
    );

    // 系统信息工具
    server.registerTool(
      'system_info',
      {
        title: 'System Info',
        description: 'Get system and server information (admin only)',
        inputSchema: {}
      },
      async () => {
        if (!this.hasScope(user, 'admin')) {
          throw new Error('Insufficient permissions: admin scope required');
        }

        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({
                server: {
                  name: 'MCP OAuth Server',
                  version: '1.0.0',
                  auth_server_url: Config.AUTH_SERVER_URL,
                  mcp_server_url: Config.MCP_SERVER_URL
                },
                users: {
                  total: UserManager.getAllUsers().length,
                  authorized_users: UserManager.getAllUsers()
                },
                scopes: {
                  supported: Config.SUPPORTED_SCOPES
                },
                environment: {
                  node_env: Config.NODE_ENV,
                  is_development: Config.IS_DEVELOPMENT
                }
              }, null, 2)
            }
          ]
        };
      }
    );

    // 资源
    server.registerResource(
      'greeting',
      new ResourceTemplate('greeting://{name}', { list: undefined }),
      {
        title: 'Greeting Resource',
        description: 'A simple greeting resource'
      },
      async (uri, { name }) => {
        return {
          contents: [{
            uri: uri.href,
            mimeType: 'text/plain',
            text: `Hello, ${name}! You are authenticated as ${user.sub}.`
          }]
        };
      }
    );

    // 提示
    server.registerPrompt(
      'user_assistant',
      {
        title: 'User Assistant',
        description: 'A helpful assistant for the authenticated user',
        argsSchema: {}
      },
      () => {
        return {
          messages: [
            {
              role: 'user',
              content: {
                type: 'text',
                text: `You are a helpful assistant for ${user.sub}. The user has permissions: ${user.scope}.`
              }
            }
          ]
        };
      }
    );

    return server;
  }

  private hasScope(user: AccessToken, scope: string): boolean {
    const userScopes = user.scope.split(' ');
    return userScopes.includes(scope);
  }



  private setupMiddleware(): void {
    // 安全中间件
    if (Config.IS_DEVELOPMENT) {
      this.app.use(helmet({
        contentSecurityPolicy: false
      }));
    } else {
      this.app.use(helmet());
    }

    // CORS 配置
    this.app.use(cors({
      origin: true,
      credentials: true,
      methods: ['GET', 'POST', 'OPTIONS', 'DELETE'],
      allowedHeaders: ['Content-Type', 'Authorization', 'Mcp-Session-Id', 'Last-Event-ID', 'Accept'],
      exposedHeaders: ['Mcp-Session-Id']
    }));

    // 解析 JSON
    this.app.use(express.json());
  }



  private setupRoutes(): void {
    // 资源服务器元数据端点
    this.app.get('/.well-known/oauth-protected-resource', (req: Request, res: Response) => {
      res.json(OAuthHandler.getResourceServerMetadata());
    });

    // MCP Streamable HTTP 端点
    this.app.all('/mcp', async (req: Request, res: Response) => {
      await this.handleMCPRequest(req, res);
    });

    // 健康检查端点
    this.app.get('/health', (req: Request, res: Response) => {
      res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        service: 'mcp-streamable-http-server'
      });
    });

    // 错误处理中间件
    this.app.use((err: any, req: Request, res: Response, next: any) => {
      console.error('MCP streamable HTTP server error:', err);
      res.status(500).json({
        error: 'server_error',
        error_description: 'Internal server error'
      });
    });

    // 404 处理
    this.app.use((req: Request, res: Response) => {
      res.status(404).json({
        error: 'not_found',
        error_description: 'Endpoint not found'
      });
    });
  }

  private async handleMCPRequest(req: Request, res: Response): Promise<void> {
    try {
      const sessionId = req.headers['mcp-session-id'] as string;

      // 检查是否有现有会话 - 如果有，直接重用，无需认证
      if (sessionId && this.authenticatedSessions.has(sessionId) && this.transports.has(sessionId)) {
        const session = this.authenticatedSessions.get(sessionId)!;
        session.lastActivity = Date.now();

        const transport = this.transports.get(sessionId)!;
        await transport.handleRequest(req, res, req.body);

        console.log(`📨 MCP Request from ${session.user.sub} (cached session): ${req.body?.method || req.method}`);
        return;
      }

      // 新会话需要认证
      const authHeader = req.headers.authorization;
      const token = authHeader && authHeader.split(' ')[1];

      if (!token) {
        // 返回 OAuth 挑战
        res.status(401).set({
          'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}"`
        }).json({
          jsonrpc: '2.0',
          error: {
            code: -32001,
            message: 'Authentication required',
            data: {
              auth_server: Config.AUTH_SERVER_URL,
              scopes: Config.SUPPORTED_SCOPES
            }
          },
          id: req.body?.id || null
        });
        return;
      }

      let user: AccessToken;
      try {
        user = JWTUtils.verifyAccessToken(token);
      } catch (error) {
        res.status(401).set({
          'WWW-Authenticate': `Bearer realm="mcp", as_uri="${Config.AUTH_SERVER_URL}"`
        }).json({
          jsonrpc: '2.0',
          error: {
            code: -32001,
            message: 'Invalid or expired access token',
            data: {
              auth_server: Config.AUTH_SERVER_URL,
              scopes: Config.SUPPORTED_SCOPES
            }
          },
          id: req.body?.id || null
        });
        return;
      }

      console.log(`📨 MCP Request from ${user.sub} (new session): ${req.body?.method || req.method}`);

      // 创建新的传输和服务器
      const transport = new StreamableHTTPServerTransport({
        sessionIdGenerator: () => this.generateSessionId(),
        onsessioninitialized: (sessionId) => {
          this.transports.set(sessionId, transport);
          // 保存认证会话
          this.authenticatedSessions.set(sessionId, {
            user,
            lastActivity: Date.now()
          });
          console.log(`🔗 New MCP session created: ${sessionId} for user ${user.sub}`);
        }
      });

      // 清理传输
      transport.onclose = () => {
        if (transport.sessionId) {
          console.log(`🔌 MCP session closed: ${transport.sessionId}`);
          this.transports.delete(transport.sessionId);
          this.authenticatedSessions.delete(transport.sessionId);
        }
      };

      // 创建 MCP 服务器实例
      const mcpServer = this.createMCPServer(user);

      // 连接到传输
      await mcpServer.connect(transport);

      // 处理请求
      await transport.handleRequest(req, res, req.body);

    } catch (error) {
      console.error('MCP request handler error:', error);
      if (!res.headersSent) {
        res.status(500).json({
          jsonrpc: '2.0',
          id: null,
          error: {
            code: -32603,
            message: 'Internal error',
            data: String(error)
          }
        });
      }
    }
  }

  private generateSessionId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  private startSessionCleanup(): void {
    // 每5分钟清理一次过期会话
    setInterval(() => {
      const now = Date.now();
      const sessionTimeout = 30 * 60 * 1000; // 30分钟超时

      for (const [sessionId, session] of this.authenticatedSessions.entries()) {
        if (now - session.lastActivity > sessionTimeout) {
          console.log(`🧹 Cleaning up expired session: ${sessionId}`);
          this.authenticatedSessions.delete(sessionId);

          // 也清理对应的传输
          if (this.transports.has(sessionId)) {
            const transport = this.transports.get(sessionId);
            transport?.close();
            this.transports.delete(sessionId);
          }
        }
      }
    }, 5 * 60 * 1000); // 5分钟间隔
  }

  async start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(Config.MCP_SERVER_PORT, () => {
          console.log(`🔧 MCP Streamable HTTP Server listening on port ${Config.MCP_SERVER_PORT}`);
          resolve();
        });

        this.server.on('error', (error: any) => {
          if (error.code === 'EADDRINUSE') {
            reject(new Error(`Port ${Config.MCP_SERVER_PORT} is already in use`));
          } else {
            reject(error);
          }
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  async stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        this.server.close(() => {
          console.log('🔧 MCP Streamable HTTP Server stopped');
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}
